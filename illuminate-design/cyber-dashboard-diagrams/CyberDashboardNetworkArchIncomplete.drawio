<mxfile host="Electron" modified="2024-05-23T10:06:33.783Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.0 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="_4gcIYRpnWTLRERq3VSD" version="24.4.0" type="device">
  <diagram name="Page-1" id="glijQeezlxbuD60vt1yS">
    <mxGraphModel dx="2488" dy="2091" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-3" value="&quot;dashboard&quot;&lt;div&gt;docker&lt;/div&gt;&lt;div&gt;(Static SPA)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="790" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-4" value="nginx proxy (docker)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="790" y="560" width="270" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="400" />
              <mxPoint x="800" y="400" />
              <mxPoint x="800" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.6;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="660" y="365" as="targetPoint" />
            <Array as="points">
              <mxPoint x="970" y="410" />
              <mxPoint x="700" y="410" />
              <mxPoint x="700" y="365" />
              <mxPoint x="612" y="365" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-5" value="&quot;csa_backend&quot;&lt;div&gt;(docker)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="940" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-6" value="MyCyberDashboard.com" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="770" y="460" width="310" height="140" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-7" value="Azure VM:&lt;div&gt;Linux&lt;/div&gt;&lt;div&gt;IP: *********** (Public)&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="697" y="600" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-10" value="CloudFlare" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-40" y="750" width="1370" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-11" target="L3hy9bulDVaJk9zXgwQJ-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="945" y="-75" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-11" value="&lt;div&gt;Schema:&lt;/div&gt;prod_cyberdashboard&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="860" y="120" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-12" value="Azure VM" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="820" y="90" width="230" height="280" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-13" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;IP:&amp;nbsp;*********&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span dir=&quot;ltr&quot; class=&quot;ui-provider a b c d e f g h i j k l m n o p q r s t u v w x y z ab ac ae af ag ah ai aj ak&quot;&gt;dash-prod-mysql-1.mysql.database.azure.com&lt;/span&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1060" y="90" width="270" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-14" value="&lt;div&gt;Schema:&lt;/div&gt;prod_qualys_vulnerabilities&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="860" y="200" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-15" value="? DB = VM or DB as a Service?" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="930" y="50" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-17" value="Azure VM" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-200" y="75" width="400" height="235" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-18" value="IP:&amp;nbsp;*********" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-523" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-19" value="&lt;div&gt;Schema:&lt;/div&gt;dev_qualys_vulnerabilities&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry y="200" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-22" value="Azure VM: dash-prod-jiraapi-1" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1170" y="135" width="240" height="185" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-81" value="Node.js" style="rounded=0;whiteSpace=wrap;html=1;" parent="L3hy9bulDVaJk9zXgwQJ-22" vertex="1">
          <mxGeometry x="160" y="130" width="80" height="55" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-23" value="IP:&amp;nbsp;*********" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1370" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-25" value="Azure VM" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="165" width="100" height="130" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-26" value="Mongo DB&lt;div&gt;Appguard&lt;/div&gt;&lt;div&gt;Jira&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=left;" parent="L3hy9bulDVaJk9zXgwQJ-25" vertex="1">
          <mxGeometry x="10" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-27" value="IP:&amp;nbsp;**********" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="300" y="165" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-29" value="&lt;span style=&quot;text-align: left;&quot;&gt;IP: *********** (Public)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="870" y="670" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-30" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Private IP: *********&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1010" y="430" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-33" target="L3hy9bulDVaJk9zXgwQJ-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-33" value="Azure NAT" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="875" y="640" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-37" target="L3hy9bulDVaJk9zXgwQJ-38" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-37" value="10.20.0.5" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="540" y="315" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-38" value="Elastic&lt;div&gt;Search&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-39" value="10.20.16.5" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="540" y="390" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-43" target="L3hy9bulDVaJk9zXgwQJ-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="590" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-43" value="BorderPoint&lt;div&gt;Agents&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="530" y="-10" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-45" value="Wazuh Server&lt;div&gt;(BoderPoint Manager)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="540" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-46" value="Public IP/Name" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="610" y="210" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-50" target="L3hy9bulDVaJk9zXgwQJ-16" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-50" value="Database &quot;Sync&quot;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="340" y="-90" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-53" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="L3hy9bulDVaJk9zXgwQJ-63" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="-80" as="sourcePoint" />
            <mxPoint x="-150" y="-80" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-105" y="-80" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-54" value="&lt;b&gt;Production&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="840" y="-150" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-55" value="&lt;b&gt;Staging&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="70" y="-150" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-56" value="Auth0 Tenant&lt;div&gt;&quot;mycyberdashboard&quot;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="780" y="810" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-57" value="Auth0 Tenant&lt;div&gt;&quot;staging-cyber-dashboard&quot;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="15" y="820" width="175" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-58" value="Auth0 Tenant&lt;div&gt;&quot;dev-cyber-dashboard&quot;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-600" y="830" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-59" value="VM" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-15" y="450" width="285" height="190" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-2" value="MyCyberDashboard.xyz&lt;div&gt;(development.mycyberdashboard.com)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="L3hy9bulDVaJk9zXgwQJ-59" vertex="1">
          <mxGeometry x="15" y="40" width="260" height="50" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-49" value="Elastic&lt;div&gt;Search&lt;/div&gt;&lt;div&gt;(dev)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-370" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-60" value="&lt;b&gt;Development&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-443" y="-150" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-61" value="IP:&amp;nbsp;**********&lt;div&gt;&lt;span style=&quot;font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, system-ui, &amp;quot;Apple Color Emoji&amp;quot;, &amp;quot;Segoe UI Emoji&amp;quot;, &amp;quot;Segoe UI Web&amp;quot;, sans-serif; font-size: 14px; white-space-collapse: preserve; background-color: initial;&quot;&gt;eng-mysql-1.mysql.database.azure.com&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="210" y="90" width="290" height="40" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-62" value="Proxy&lt;div&gt;Port Fwd&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-530" y="350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-16" value="&lt;div&gt;Schema:&lt;/div&gt;preprod_cyberdashboard&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry y="120" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-63" value="&lt;div&gt;Schema:&lt;/div&gt;dev_cyberdashboard&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="-190" y="120" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-64" value="dashboard-prod&lt;div&gt;10.0.2.9&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-560" y="550" width="213" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-65" value="&lt;div&gt;Schema:&lt;/div&gt;dash_vulnerabilities&lt;div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="860" y="280" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-66" value="Azure&lt;div&gt;Load Balancers&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="450" y="330" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-73" value="Dash-Prod-AIO-1" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1070" y="460" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" target="L3hy9bulDVaJk9zXgwQJ-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" target="L3hy9bulDVaJk9zXgwQJ-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-85" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1540" y="-10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-95" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" target="L3hy9bulDVaJk9zXgwQJ-94" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" target="L3hy9bulDVaJk9zXgwQJ-91" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-72" value="Python scripts" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1181" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-72" target="L3hy9bulDVaJk9zXgwQJ-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-24" value="Mongo DB&lt;div&gt;Appguard&lt;/div&gt;&lt;div&gt;Jira&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=left;" parent="1" vertex="1">
          <mxGeometry x="1211" y="230" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-65" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="380" />
              <mxPoint x="840" y="380" />
              <mxPoint x="840" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-79" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1600" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-79" value="Server: ENG-MP-1&lt;div&gt;Python script&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="1520" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-80" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-79" target="L3hy9bulDVaJk9zXgwQJ-65" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1580" y="390" />
              <mxPoint x="1120" y="390" />
              <mxPoint x="1120" y="333" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-82" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-81" target="L3hy9bulDVaJk9zXgwQJ-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-83" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.371;entryY=0.964;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-5" target="L3hy9bulDVaJk9zXgwQJ-81" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-88" value="AppGuard&lt;div&gt;US&lt;/div&gt;&lt;div&gt;SQL&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="1540" y="-80" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-89" value="AppGuard&lt;div&gt;UK&lt;/div&gt;&lt;div&gt;SQL&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
          <mxGeometry x="1500" y="-110" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-91" value="Jira&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1800" y="30" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-92" value="Private: Our Serve - private azure network" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1450" y="-140" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-94" value="Qualys&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1800" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-100" value="Border Point&lt;div&gt;(Manager &amp;amp; Elastic)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
          <mxGeometry x="1534" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-101" value="AppGuard" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1630" y="-155" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-102" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;" parent="1" source="L3hy9bulDVaJk9zXgwQJ-101" target="L3hy9bulDVaJk9zXgwQJ-88" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-103" value="VM: MP1" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1497" y="350" width="166" height="155" as="geometry" />
        </mxCell>
        <mxCell id="L3hy9bulDVaJk9zXgwQJ-104" value="VM runs other things but the script could move to the other VM" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1670" y="345" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="HIiY89pRO1YCVpGBbxwF-1" value="Dan&#39;s script test area" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1690" y="410" width="130" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
