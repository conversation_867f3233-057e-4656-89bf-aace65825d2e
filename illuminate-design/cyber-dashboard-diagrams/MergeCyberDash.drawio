<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram name="Page-1" id="9vvs7wq4BB8GzXdgUway">
    <mxGraphModel dx="2054" dy="1078" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cg_oWV8pGt9hVNMA4BHI-4" target="cg_oWV8pGt9hVNMA4BHI-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-4" value="API Server&lt;div&gt;? &quot;Hermes&quot; ?&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="610" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-7" value="Local DB&lt;div&gt;(Data Cache?)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="540" y="450" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="cg_oWV8pGt9hVNMA4BHI-4" target="cg_oWV8pGt9hVNMA4BHI-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-10" value="SaaS" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="720" y="470" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-11" value="Azure Hosted&lt;div&gt;(AppGuard,&lt;/div&gt;&lt;div&gt;BP,&lt;/div&gt;&lt;div&gt;OpenProject)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="690" y="620" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-15" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;Zeus DB&lt;/h1&gt;&lt;div&gt;Users,&amp;nbsp;&lt;/div&gt;&lt;div&gt;Companies,&amp;nbsp;&lt;/div&gt;&lt;div&gt;Products&lt;/div&gt;&lt;div&gt;Services,&amp;nbsp;&lt;/div&gt;&lt;div&gt;&quot;Service Allocation&quot;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;b&gt;NO Service Data&lt;/b&gt;&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="20" y="440" width="130" height="160" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-17" value="Zeus" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-18" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="270" y="920" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-19" value="Athena&lt;div&gt;(UI Code Server)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="165" y="660" width="105" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-20" value="Browser&lt;div&gt;facilitate / illuminate&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="225" y="845" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-21" value="Zeus" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="610" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-22" value="Athena&lt;div&gt;(UI Code Server)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="175" y="670" width="105" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-23" value="ELB" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="210" y="750" width="150" height="20" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-24" value="Zeus&lt;div&gt;Postgres&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="140" y="540" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-25" value="Auth0" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="740" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-26" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="850" as="sourcePoint" />
            <mxPoint x="40" y="800" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-27" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="630" as="sourcePoint" />
            <mxPoint x="-20" y="720" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="640" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-28" value="Machine&lt;div&gt;Auth&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-10" y="640" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-29" value="HTTP&lt;div&gt;Auth: JWT (User)&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="790" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-30" value="HTTP&lt;div&gt;Auth: JWT (Machine)&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="420" y="600" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-31" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="640" as="sourcePoint" />
            <mxPoint x="510" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.033;entryY=0.614;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="cg_oWV8pGt9hVNMA4BHI-4" target="cg_oWV8pGt9hVNMA4BHI-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="cg_oWV8pGt9hVNMA4BHI-17" target="cg_oWV8pGt9hVNMA4BHI-24">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="300" y="580" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-35" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="288.5" y="835" as="sourcePoint" />
            <mxPoint x="288.5" y="775" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cg_oWV8pGt9hVNMA4BHI-36" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="299" y="745" as="sourcePoint" />
            <mxPoint x="299" y="685" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
