CSA Dashboard validation rules

General concept
A user is able to see details for their own company (ie GBMS) + the details of any company which lists (GBMS) as it's parent company.
A user who is admin for the company (ie GBMS) has full control (edit) of all (GBMS) users as well as all users from child companies.
A user whos is not admin for the company (GBMS) cannot edit (GBMS) users, but can edit all users of child company.

Special validation rules for CSA
CSA does not have a parent company

Company Administation
User should only see those companies for which they have permissions for. (ie - their own company + any child companies)
Only CSA users should see "Add a new company" and only CSA users should be able to access the page to add a new company.
Only CSA users should see the "Edit" button. Other users should only see a "View" button.  (Maybe all users should see view?)

Add new company
Only CSA users should be able to load this page.  It should not be possible for non CSA users to add a new company.
The Parent Company dropdown list should be populated with all companies where partner = yes

View/Edit Company
Only CSA users should be able to edit the company.
The company users box should show all users who are part of this company
The child companies box should not be visible to users who belong to a company which is not a partner
The child companies box should not be visible if there are no child companies
The child companies box should show all companies which has this company listed as their parent
The inherited users box should not be visible to users who belong to a company which is not a partner
The inherited users box should show all users who can select this company to view data.  (For example, First Heritage is the company - GBMS (who are the parent) users will be listed and also CSA users will be listed because they are the parent of GBMS

User administration
Users should see all users, from their own company plus any companies which are a child of the company.
All users should be able to access the add new user page
A user can view details of all users from their own company and any child companies
In order to edit users of their own company, the user must be a company admin
A user can edit all users belonging to a child company.
(Maybe the edit button should be view instead of edit?)

Add new user
If a user is a company administrator, they can add users to their own company.
A user can add a new user for any child company

View/Edit user
A user cannot edit users associated with their own company, unless they are company admin
A user can edit all users associated with a child company
The child companies should show all child companies that the user should have access to see