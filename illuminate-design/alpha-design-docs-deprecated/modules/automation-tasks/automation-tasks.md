The Automation Task Module.

Could involve;

    DB Element (own db to record whatever in)
    UI Element (own UI to query/record things)
    Web Server Element (Receives request from browser to run a task or get results of a task)
    Serverless Element (the tasks we actually run)

As the tasks are themselves lambdas we could just grab the logs and executions from AWS
and then show in our GUI something a bit like cloud formation where for any given task
we can show when it was run and what the result was, and can query the logs for that
task and show in the GUI

      	Every automation task is a lambda and we can know or enumerate on demand
      		(A) the list of lambdas that are automation tasks (eg by name)
      		(B) For each of them: see result status, grab logs, etc
