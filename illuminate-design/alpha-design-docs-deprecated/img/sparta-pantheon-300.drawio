<mxfile host="Electron" modified="2024-06-06T09:56:16.889Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="n0pk3f00w5VpAUZu7iXr" version="24.4.13" type="device">
  <diagram name="Page-1" id="GS79sGuyBNBXe_ZdaTQw">
    <mxGraphModel dx="724" dy="489" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="zaeGI2J4pg3mENvd5ihf-9" value="Sparta Project" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="40" y="310" width="580" height="340" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-8" value="Pantheon Platform" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="310" y="350" width="290" height="290" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" source="zaeGI2J4pg3mENvd5ihf-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-50" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-6" value="Hermes&lt;div&gt;(Messenger)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" vertex="1">
          <mxGeometry x="20" y="30" width="250" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" source="zaeGI2J4pg3mENvd5ihf-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-50" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-5" value="Zeus&lt;div&gt;(Web Server)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" vertex="1">
          <mxGeometry x="20" y="110" width="250" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="zaeGI2J4pg3mENvd5ihf-8" source="zaeGI2J4pg3mENvd5ihf-5" target="zaeGI2J4pg3mENvd5ihf-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-2" value="Athena&lt;div&gt;(Web UI)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" vertex="1">
          <mxGeometry x="20" y="190" width="250" height="70" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" source="zaeGI2J4pg3mENvd5ihf-2" target="zaeGI2J4pg3mENvd5ihf-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-18" value="illuminate" style="rounded=1;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" vertex="1">
          <mxGeometry x="20" y="260" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-17" value="facilitate" style="rounded=1;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-8" vertex="1">
          <mxGeometry x="150" y="260" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-10" value="Automation, Integration &amp;amp; Feature Modules" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="60" y="350" width="195" height="290" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-11" value="ITSM&lt;div&gt;Integration Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-10" vertex="1">
          <mxGeometry x="10" y="40" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-13" value="Qualys&lt;div&gt;Integration Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-10" vertex="1">
          <mxGeometry x="10" y="100" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-14" value="Task Automation&lt;div&gt;Feature Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-10" vertex="1">
          <mxGeometry x="10" y="170" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="zaeGI2J4pg3mENvd5ihf-20" value="Pen Test&lt;div&gt;Feature Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="zaeGI2J4pg3mENvd5ihf-10" vertex="1">
          <mxGeometry x="10" y="230" width="170" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
