# Sparta

Welcome to Project SPARTA.

*The Vision:* 

>To provide a seamless platform for our clients, partners and employees, that provides the gateway to all of our services.

*The Mission:*

>* Improve the customer journey from start to finish
>* Streamline the internal processes through the use of AI / Automation(s)
>* Develop a truly unique platform / offering to improve the Cyber Security Ecosystem

### High Level Business Requirements

* Internal & customer facing websites
* Customer External API
* Talk to a variety of third party systems
* "Single Pane of Glass"
* Display and update ITSM Tickets (Halo)
* Support Task Automation
* Integrate Cyber Dashboard & SureCloud Platform

### Illuminate & Facilitate

Customer facing website will be the "Illuminate Platform".

Internal facing website will be the "Facilitate Platform".

### Products & Services

The business plans to offer a variety of products and services. 

Core initial plans include;
* Managed Detection and Response (MDR)
* Vulnerability Management as a Service (VMaaS)
* eLearning
* Consultancy Assessments
* Penetration Testing as a Service (PTaaS)
* Red Teaming and Adversary Simulation
* Mobile Endpoint Security (MES)
* Specific third party product management (offered as a service by us)

Other things might be added, we might never see all of the above.

To provide these we will need to integrate with a variety of third party systems.

### Sparta, Pantheon & Modules

The system takes a modular approach to delivering functionality.

Sparta is the whole project. Pantheon Platform provides a generic integration and
modular framework which has no knowledge of actual integrations or feature modules.

![](./img/sparta-pantheon-300.drawio.png)

Integrating with a third party solution to provide a service gets its own module. 

The required task automation feature of the system is itself implemented as a module.

