# Corporate Data

Rough notes, to be fleshed out during prototyping.

```
Corporate DAta

corporate-data-and-service-provision
    - Company Created,Renamed,Deleted, Service Provisioned
            Generic message but hermes can take and call an API function
            Hermes events are also lambda executions which we can list, view logs, etc. (can make these visible in the UI)


=== Securing data by company
Corporate Identity for independent services
Events: new company, provision service x for company, de-commission service x
permissions, authentication:
  Who is allowed to call the lambda (Zeus is allowed)
  but how are we ensuring User A of company Y only gets Company Y's data and can't request data from company X
  Parameters to the functions but if we expose by API gateway internally we may still
  want JWT auth and/or checks on which company source request is from.

```
