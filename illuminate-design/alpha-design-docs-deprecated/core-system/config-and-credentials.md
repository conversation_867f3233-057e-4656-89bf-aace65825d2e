# Config & Credentials

Rough notes, to be fleshed out

```
Config & credentials:

    Some kind of global and module config objects
    Global includes feature flags which might be shared and a list of enabled modules.

    configuration
        Define env var, config system, permissions
        Feature Flags
      Env Vars for Docker container or dev start up.
      Credentials for talking to <PERSON> are provided as environment vars
      For AWS store in SSM parameters
      <PERSON><PERSON> can be allowed to read SSM parameters (could be put in LocalStack locally)
      Local Code, Docker & Lambda could read SSM 
      or we could have local web/docker read env var (like redscan)
      &
      Lambda
      https://docs.aws.amazon.com/systems-manager/latest/userguide/ps-integration-lambda-extensions.html
      https://docs.aws.amazon.com/secretsmanager/latest/userguide/retrieving-secrets_lambda.html
      although might be easier to use SDK

      Global credentials to talk to a service could be SSM parameters
      Credentials to talk to a service on per company basis belong in the DB for that module
      These must be stored encrypted  

```
