# Permissions

We MUST have some kind of role based permissions system, RBAC.

We have to consider that some permissions may relate to a specific module. 
A module might need to make permissions available that are only for internal use rather than
items that a cutomer could see.

One approach would be all permission definitions are just data that gets added to
the core database via dbChanges. When a user make a web request to a web module the
user context including user id and permissions could be added before hitting that
controller/manager/service.

Complications to be considered include where a permission might be needed to fetch data
via the zeus proxy subsystem. Perhaps that forces a web module that defines the protected
endpoint that then requests from a zeus internal service that is the proxy subsystem. Probably
better than having a the proxy try to calculate internal destinations based on incoming path/data
as that might be exploitable in some way.

Still a bit vague and some prototyping to flesh out.

For now keep permissions tied to the concept of 'role', keep the concept of 'Team' as something we need for
other reasons.

Probably
```
Flat permission structure
deny by default
Roles grant permissions x,y,z
with support for explicit deny of permission
explicit deny > explicit grant

```
