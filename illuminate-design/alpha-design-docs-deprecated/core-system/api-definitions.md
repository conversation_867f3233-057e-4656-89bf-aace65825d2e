# API Definitions

* Internal API
* Client APIs: facilitate & illuminate
* External API - Public facing API for customers (eventual goal)

#  api paths
gateway always have some schema like
```
  /internal/module-name/endpoint
```
This would allow hermes or other system to get a message that is more json fields for module-name, 
function-name, etc which it can then map to work our a url.

### Versioning

All APIs MUST be versioned. Versioned at the api/module level, not on a per endpoint basis.

### Access Control

Each API needs JWT token, unique audience for each api.

Our internal API still needs a JWT token but beyond that will 'trust' its clients with data for
any customer. It then becomes the responsibility of Zeus or any other client to validate and
restrict access to specific data obtainable from the internal API.

#### Automation Task Authentication
How is Automation task request authorized?
- Simple solution is we give it a client id/secret that it MUST use to get a JWT token.
  It can then make requests of the internal API
