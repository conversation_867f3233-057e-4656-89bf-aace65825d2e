# Zeus Server

Rough Notes

```
- Can make use of throttling offered by nest.js
Zeus Modules
Zeus Proxy Component
- The modular/dynamic data
Avoid giving url to client, avoid server side request forgery
client can't send url to server
but must request the type of data and the server look up the correct url

Might offer some "services" (code services, not product services) to the web server modules to allow access
to some common things. Need to be defined a common library so the library is imported by core and modules.

```

To flesh out during/after prototyping
