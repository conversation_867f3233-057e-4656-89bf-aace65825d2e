<mxfile host="Electron" modified="2024-05-24T10:11:27.535Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.0 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="6QOTrVZyA6vxhFNqO3w_" version="24.4.0" type="device">
  <diagram name="Page-1" id="bCUn2duOVwePQm2JIUof">
    <mxGraphModel dx="3734" dy="1393" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Fs62fKfwAVcooMCmPz_N-83" value="AWS Staging Account" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-520" y="290" width="673" height="370" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-12" value="Internet" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="40" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-14" value="Saas Delta" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="90" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-24" value="Prod Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="440" y="320" width="190" height="250" as="geometry">
            <mxRectangle x="440" y="320" width="90" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-20" value="&lt;div&gt;Sparta&lt;/div&gt;Production" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-24">
          <mxGeometry x="30" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-22" value="RDS&lt;div&gt;Prod DB(s)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-24">
          <mxGeometry x="60" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-24" source="Fs62fKfwAVcooMCmPz_N-20" target="Fs62fKfwAVcooMCmPz_N-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-27" value="CSA Production&lt;div&gt;(non Sparta)&lt;/div&gt;" style="swimlane;whiteSpace=wrap;html=1;startSize=40;" vertex="1" parent="1">
          <mxGeometry x="420" y="650" width="280" height="230" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-15" value="DB" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-27">
          <mxGeometry x="180" y="90" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-16" value="Service" style="whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-27">
          <mxGeometry x="60" y="90" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-17" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;CSA Production&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;(Azure, On Prem,&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;Another CSA AWS&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Account)&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-27">
          <mxGeometry x="-5" y="40" width="270" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-27" source="Fs62fKfwAVcooMCmPz_N-29" target="Fs62fKfwAVcooMCmPz_N-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-29" value="Dev VM" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/Virtual_Machine_2.svg;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-27">
          <mxGeometry x="10" y="184" width="50" height="46" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-27" source="Fs62fKfwAVcooMCmPz_N-29" target="Fs62fKfwAVcooMCmPz_N-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-28" value="" style="shape=mxgraph.cisco.computers_and_peripherals.laptop;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="-790" y="849" width="90" height="61" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-33" value="PreProd Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="223" y="320" width="190" height="250" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-34" value="&lt;div&gt;Sparta&lt;/div&gt;PreProd" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-33">
          <mxGeometry x="30" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-35" value="RDS&lt;div&gt;Pre-Prod DB(s)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-33">
          <mxGeometry x="60" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-33" source="Fs62fKfwAVcooMCmPz_N-34" target="Fs62fKfwAVcooMCmPz_N-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-41" value="CLONE" style="shape=flexArrow;endArrow=classic;html=1;rounded=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-33">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="267" y="69.47000000000003" as="sourcePoint" />
            <mxPoint x="127" y="69.47000000000003" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-42" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-33" target="Fs62fKfwAVcooMCmPz_N-27">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="610" as="sourcePoint" />
            <mxPoint x="279.0625" y="754.9999999999995" as="targetPoint" />
            <Array as="points">
              <mxPoint x="320" y="765" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-44" value="Read-Only&lt;div&gt;Credentials&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="250" y="675" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-45" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="570" as="sourcePoint" />
            <mxPoint x="540" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-46" value="AWS Prod Account" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="200" y="290" width="450" height="290" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-52" target="Fs62fKfwAVcooMCmPz_N-57">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-52" value="AWS QA Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="-497" y="320" width="200" height="250" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-52" source="Fs62fKfwAVcooMCmPz_N-53" target="Fs62fKfwAVcooMCmPz_N-58">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-53" value="&lt;div&gt;Sparta&lt;/div&gt;QA" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-52">
          <mxGeometry x="30" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-54" value="RDS&lt;div&gt;QA&lt;/div&gt;&lt;div&gt;DB(s)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-52">
          <mxGeometry x="30" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-52" source="Fs62fKfwAVcooMCmPz_N-53" target="Fs62fKfwAVcooMCmPz_N-54">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-58" value="Mock&lt;div&gt;Service Z&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-52">
          <mxGeometry x="120" y="60" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-57" value="Shared Mock&lt;div&gt;Service X, Y, ...&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-315" y="600" width="270" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-62" target="Fs62fKfwAVcooMCmPz_N-57">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-62" value="AWS PenTest Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="-280" y="320" width="200" height="250" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-62" source="Fs62fKfwAVcooMCmPz_N-64" target="Fs62fKfwAVcooMCmPz_N-67">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-64" value="&lt;div&gt;Sparta&lt;/div&gt;&lt;div&gt;PenTest&lt;/div&gt;" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-62">
          <mxGeometry x="30" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-65" value="RDS&lt;div&gt;PenTest&lt;/div&gt;&lt;div&gt;DB(s)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-62">
          <mxGeometry x="30" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-66" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-62" source="Fs62fKfwAVcooMCmPz_N-64" target="Fs62fKfwAVcooMCmPz_N-65">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-67" value="Mock&lt;div&gt;Service Z&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-62">
          <mxGeometry x="120" y="60" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-68" target="Fs62fKfwAVcooMCmPz_N-57">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-68" value="AWS UAT Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="-70" y="320" width="200" height="250" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-69" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-68" source="Fs62fKfwAVcooMCmPz_N-70" target="Fs62fKfwAVcooMCmPz_N-73">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-70" value="&lt;div&gt;Sparta&lt;/div&gt;&lt;div&gt;UAT&lt;/div&gt;" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-68">
          <mxGeometry x="30" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-71" value="RDS&lt;div&gt;UAT&lt;/div&gt;&lt;div&gt;DB(s)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-68">
          <mxGeometry x="30" y="30" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Fs62fKfwAVcooMCmPz_N-68" source="Fs62fKfwAVcooMCmPz_N-70" target="Fs62fKfwAVcooMCmPz_N-71">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-73" value="Mock&lt;div&gt;Service Z&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-68">
          <mxGeometry x="120" y="60" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-79" value="Public SaaS Alpha" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="90" y="70" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-78" value="Test Tenant/Project" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-79">
          <mxGeometry y="30" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-77" value="Production&lt;div&gt;Tenant/Project&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-79">
          <mxGeometry x="80" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-80" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-24" target="Fs62fKfwAVcooMCmPz_N-77">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="130" as="sourcePoint" />
            <mxPoint x="460" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="530" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-81" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" target="Fs62fKfwAVcooMCmPz_N-77">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="320" as="sourcePoint" />
            <mxPoint x="200" y="485" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-82" value="Read-Only&lt;div&gt;Credentials&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="260" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-85" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;entryX=-0.019;entryY=0.352;entryDx=0;entryDy=0;exitX=0.436;exitY=-0.002;exitDx=0;exitDy=0;entryPerimeter=0;exitPerimeter=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-68" target="Fs62fKfwAVcooMCmPz_N-78">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-35" y="170" as="sourcePoint" />
            <mxPoint x="-280" y="50" as="targetPoint" />
            <Array as="points">
              <mxPoint x="17" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-86" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;exitX=0.886;exitY=-0.009;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-62">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-190" y="320" as="sourcePoint" />
            <mxPoint x="90" y="121" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-103" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-87" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;exitX=0.886;exitY=-0.009;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.003;entryY=0.352;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" target="Fs62fKfwAVcooMCmPz_N-78">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-315" y="320" as="sourcePoint" />
            <mxPoint x="-122" y="123" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-315" y="122" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-88" value="AWS Dev Account" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-900" y="380" width="190" height="150" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-5" value="Various&lt;div&gt;AWS Resources&lt;/div&gt;&lt;div&gt;(TBD)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-88">
          <mxGeometry x="10" y="30" width="160" height="93.75" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-89" value="Docker" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-790" y="660" width="210" height="166" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-90" value="LocalStack&lt;div&gt;(Virtual AWS)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-89">
          <mxGeometry x="80" y="31" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-91" value="Test Container" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-89">
          <mxGeometry x="80" y="106" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-92" value="DB(s)" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-89">
          <mxGeometry y="86" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-96" value="Mock" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="Fs62fKfwAVcooMCmPz_N-89">
          <mxGeometry x="10" y="31" width="50" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-93" value="DB(s)" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="-850" y="746" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-94" value="Local&lt;div&gt;Processes/Code&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-920" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-95" value="Mock" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-920" y="751" width="50" height="70" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-101" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-97">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-830" y="530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-97" value="Developer Machine" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-940" y="629" width="380" height="210" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-98" value="Dev" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="-835" y="849" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-100" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0.02;entryY=0.554;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="Fs62fKfwAVcooMCmPz_N-97" target="Fs62fKfwAVcooMCmPz_N-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-102" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF3333;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-650" y="620" as="sourcePoint" />
            <mxPoint x="90" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-650" y="121" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-103" value="Controlled access to VM only for purpose of creating mock of system without test instances" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-350" y="760" width="250" height="89" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-109" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Sparta Environment &quot;Vision&quot;&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-950" y="55" width="280" height="45" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-110" value="read/write" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-357" y="90" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-111" value="read/write" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="540" y="240" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-112" value="read/write" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="550" y="584.5" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-113" value="On Demand Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="-270" y="920" width="190" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Fs62fKfwAVcooMCmPz_N-114" value="Permanent Environment" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-50" y="920" width="190" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
