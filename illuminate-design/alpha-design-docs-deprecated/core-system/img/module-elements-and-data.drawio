<mxfile host="Electron" modified="2024-06-07T12:53:02.080Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="pwLwWe_u7Sy_fj22b_Pr" version="24.4.13" type="device">
  <diagram name="Page-1" id="6LGK4Su0pQwyO3rIxdAI">
    <mxGraphModel dx="1521" dy="1026" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-49" value="User Interface" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=bottom;" parent="1" vertex="1">
          <mxGeometry x="295" y="830" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-1" value="Module&lt;div&gt;Data&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="482.75" y="190" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-3" target="nwfjMcQ5uyU0IKZffR8X-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-3" value="Serverless Module Element" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="140" y="240" width="200" height="230" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-39" value="&lt;div style=&quot;&quot;&gt;Data Presenter&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=right;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="90" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-40" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;container=1;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="90" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-41" value="&lt;div style=&quot;&quot;&gt;Other Function&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=right;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="150" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-42" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;container=1;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="150" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-4" value="&lt;div style=&quot;&quot;&gt;Data Collector&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=right;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="30" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-5" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;container=1;" parent="nwfjMcQ5uyU0IKZffR8X-3" vertex="1">
          <mxGeometry x="30" y="31" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-9" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="490" y="325" width="141" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-9" source="nwfjMcQ5uyU0IKZffR8X-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-40" y="65" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-10" value="AWS&lt;div&gt;Event Bridge&lt;/div&gt;&lt;div&gt;(Scheduler)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-9" vertex="1">
          <mxGeometry width="141" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-11" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.eventbridge;" parent="nwfjMcQ5uyU0IKZffR8X-9" vertex="1">
          <mxGeometry width="31" height="31" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-13" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="362.75" y="400" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-14" value="Hermes Event Queue.&amp;nbsp;" style="rounded=0;whiteSpace=wrap;html=1;align=right;" parent="nwfjMcQ5uyU0IKZffR8X-13" vertex="1">
          <mxGeometry width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-15" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sqs;" parent="nwfjMcQ5uyU0IKZffR8X-13" vertex="1">
          <mxGeometry x="2" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-16" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="15" y="530" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-17" value="AWS API Gateway&lt;div&gt;(Internal Access Only)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="nwfjMcQ5uyU0IKZffR8X-16" vertex="1">
          <mxGeometry width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-18" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.api_gateway;fillColor=#D9A741;gradientColor=none;" parent="nwfjMcQ5uyU0IKZffR8X-16" vertex="1">
          <mxGeometry x="4" width="46" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-20" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="305.5" y="525" width="134.5" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-12" value="Hermes&lt;div&gt;Messenger&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-20" vertex="1">
          <mxGeometry width="134.5" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-19" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" parent="nwfjMcQ5uyU0IKZffR8X-20" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-21" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="510" y="500" width="160" height="45" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-22" value="Zeus Event Queue.&amp;nbsp;" style="rounded=0;whiteSpace=wrap;html=1;align=right;" parent="nwfjMcQ5uyU0IKZffR8X-21" vertex="1">
          <mxGeometry width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-23" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sqs;" parent="nwfjMcQ5uyU0IKZffR8X-21" vertex="1">
          <mxGeometry x="1.67" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-27" value="Third Party&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-3" target="nwfjMcQ5uyU0IKZffR8X-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-17" target="nwfjMcQ5uyU0IKZffR8X-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="240" as="targetPoint" />
            <mxPoint x="475" y="750" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="750" y="750" />
              <mxPoint x="750" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-14" target="nwfjMcQ5uyU0IKZffR8X-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-12" target="nwfjMcQ5uyU0IKZffR8X-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-37" target="nwfjMcQ5uyU0IKZffR8X-32" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-37" value="Athena&lt;div&gt;UI Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="375" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="650" y="375" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-44" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Module&lt;div&gt;Scheduled&lt;div&gt;Task&lt;/div&gt;&lt;div&gt;Definition&lt;/div&gt;&lt;/div&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="650" y="245" width="80" height="110" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-50" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="380" y="940" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-52" value="Zeus Server" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="610" width="490" height="180" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" parent="nwfjMcQ5uyU0IKZffR8X-52" source="nwfjMcQ5uyU0IKZffR8X-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="360" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-32" value="Zeus Web&lt;div&gt;server&lt;/div&gt;&lt;div&gt;module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;verticalAlign=bottom;fontStyle=1" parent="nwfjMcQ5uyU0IKZffR8X-52" vertex="1">
          <mxGeometry x="165" y="30" width="160" height="115" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-47" value="Zeus&lt;div&gt;Message&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;Receiver&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-52" vertex="1">
          <mxGeometry x="361.67" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-46" value="Zeus Proxy&lt;div&gt;subsystem&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-52" vertex="1">
          <mxGeometry x="20" y="85" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-64" value="&lt;span style=&quot;background-color: initial;&quot;&gt;Message&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Sender&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="nwfjMcQ5uyU0IKZffR8X-52" vertex="1">
          <mxGeometry x="361.67" y="85" width="118.33" height="35" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-12" target="nwfjMcQ5uyU0IKZffR8X-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.56;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-22" target="nwfjMcQ5uyU0IKZffR8X-47" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-47" target="nwfjMcQ5uyU0IKZffR8X-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0.692;entryY=0.983;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-37" target="nwfjMcQ5uyU0IKZffR8X-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="405" y="770" />
              <mxPoint x="253" y="770" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.322;entryY=0.95;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="713" as="sourcePoint" />
            <mxPoint x="89.05999999999995" y="585" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-48" value="Module&lt;div&gt;Message&lt;/div&gt;&lt;div&gt;Handler&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="392.75" y="640" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-64" target="nwfjMcQ5uyU0IKZffR8X-14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="998.1500000000001" y="715" as="sourcePoint" />
            <mxPoint x="710" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="690" y="713" />
              <mxPoint x="690" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
