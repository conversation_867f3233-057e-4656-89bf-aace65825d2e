<mxfile host="Electron" modified="2024-06-17T12:36:50.729Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.5.3 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="lZEtrMEWDf383fmvkpd1" version="24.5.3" type="device">
  <diagram name="Page-1" id="6LGK4Su0pQwyO3rIxdAI">
    <mxGraphModel dx="799" dy="890" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-49" value="User Interface" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=bottom;" parent="1" vertex="1">
          <mxGeometry x="295" y="830" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-1" value="Module&lt;div&gt;Data&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="482.75" y="190" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.094;exitY=0;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-32" target="nwfjMcQ5uyU0IKZffR8X-27" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="140" y="355" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-9" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="490" y="325" width="141" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-10" value="AWS&lt;div&gt;Event Bridge&lt;/div&gt;&lt;div&gt;(Scheduler)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="nwfjMcQ5uyU0IKZffR8X-9" vertex="1">
          <mxGeometry width="141" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-11" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.eventbridge;" parent="nwfjMcQ5uyU0IKZffR8X-9" vertex="1">
          <mxGeometry width="31" height="31" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-27" value="Third Party&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="240" as="targetPoint" />
            <mxPoint x="475" y="750" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="750" y="750" />
              <mxPoint x="750" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-37" target="nwfjMcQ5uyU0IKZffR8X-32" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-37" value="Athena&lt;div&gt;UI Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="375" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="650" y="375" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-44" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;Module&lt;div&gt;Scheduled&lt;div&gt;Task&lt;/div&gt;&lt;div&gt;Definition&lt;/div&gt;&lt;/div&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="650" y="245" width="80" height="110" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-50" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="380" y="940" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-52" value="Zeus Server" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="150" y="610" width="490" height="180" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-32" value="Zeus Web&lt;div&gt;server&lt;/div&gt;&lt;div&gt;module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;verticalAlign=bottom;fontStyle=1" parent="nwfjMcQ5uyU0IKZffR8X-52" vertex="1">
          <mxGeometry x="165" y="30" width="160" height="115" as="geometry" />
        </mxCell>
        <mxCell id="nwfjMcQ5uyU0IKZffR8X-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.863;entryY=-0.017;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="nwfjMcQ5uyU0IKZffR8X-10" edge="1" target="nwfjMcQ5uyU0IKZffR8X-32">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="450" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
