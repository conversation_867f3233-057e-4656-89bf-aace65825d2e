<mxfile host="Electron" modified="2024-06-17T12:36:52.343Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.5.3 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="aFyeaehjgtfXf-5_x4jN" version="24.5.3" type="device">
  <diagram name="Page-1" id="4glk6X5BNslhmnv0eetB">
    <mxGraphModel dx="1302" dy="1023" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="EGFJhHhph9IvvOz9PGOY-76" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="810" y="680" width="169.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-77" value="Zeus&lt;div&gt;(Sparta Core Web Server&lt;span style=&quot;background-color: initial;&quot;&gt;)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="EGFJhHhph9IvvOz9PGOY-76" vertex="1">
          <mxGeometry width="169.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-78" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;" parent="EGFJhHhph9IvvOz9PGOY-76" vertex="1">
          <mxGeometry y="1" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-1" value="PostgreSQL RDS" style="swimlane;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="719.75" y="140" width="310" height="305" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-2" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.rds;" parent="EGFJhHhph9IvvOz9PGOY-1" vertex="1">
          <mxGeometry y="26" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-3" value="Other&lt;div&gt;Module&lt;/div&gt;&lt;div&gt;Data&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="EGFJhHhph9IvvOz9PGOY-1" vertex="1">
          <mxGeometry x="10" y="80" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-4" value="Zeus&lt;div&gt;Data&lt;/div&gt;&lt;div&gt;Schema&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="EGFJhHhph9IvvOz9PGOY-1" vertex="1">
          <mxGeometry x="110" y="205" width="70" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-5" value="Module&lt;div&gt;Data&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="EGFJhHhph9IvvOz9PGOY-1" vertex="1">
          <mxGeometry x="10" y="180" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-6" value="Companies&lt;div&gt;Users&lt;br&gt;&lt;div&gt;Services&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="EGFJhHhph9IvvOz9PGOY-1" vertex="1">
          <mxGeometry x="170" y="225" width="80" height="67.5" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-13" value="Third Party&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="90" y="150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-14" value="Third Party&lt;div&gt;SaaS&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="90" y="385" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-15" value="Auth0" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="140" y="920" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="EGFJhHhph9IvvOz9PGOY-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="884.75" y="730" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-32" value="AWS ELB" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="709.75" y="755" width="350" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-33" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.elastic_load_balancing;fillColor=#F58534;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="709.75" y="760" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-59" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="800" y="670" width="169.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-35" value="Zeus&lt;div&gt;(Sparta Core Web Server&lt;span style=&quot;background-color: initial;&quot;&gt;)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="EGFJhHhph9IvvOz9PGOY-59" vertex="1">
          <mxGeometry width="169.5" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-36" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;" parent="EGFJhHhph9IvvOz9PGOY-59" vertex="1">
          <mxGeometry y="1" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-60" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="796" y="937" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-61" target="EGFJhHhph9IvvOz9PGOY-32" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-61" value="CloudFlare" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="739.75" y="820" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-62" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="752" y="958" as="sourcePoint" />
            <mxPoint x="292" y="955.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-63" value="Authenticate" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="697" y="958" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-65" value="Customer&lt;div&gt;Process&lt;/div&gt;&lt;div&gt;(Calls APIs)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="917.5" y="903" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-66" value="HTTP Requests" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="790" y="860" width="115" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-69" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="763" y="903" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-67" value="Browser" style="rounded=0;whiteSpace=wrap;html=1;" parent="EGFJhHhph9IvvOz9PGOY-69" vertex="1">
          <mxGeometry width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-68" value="" style="dashed=0;outlineConnect=0;html=1;align=center;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;shape=mxgraph.weblogos.chrome" parent="EGFJhHhph9IvvOz9PGOY-69" vertex="1">
          <mxGeometry width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-71" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.556;entryY=1.067;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-65" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="982" y="890" as="sourcePoint" />
            <mxPoint x="904.9999999999998" y="850" as="targetPoint" />
            <Array as="points">
              <mxPoint x="982" y="903" />
              <mxPoint x="982" y="888" />
              <mxPoint x="905" y="888" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.542;entryY=1.067;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-67" target="EGFJhHhph9IvvOz9PGOY-61" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="823" y="890" />
              <mxPoint x="897" y="890" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-91" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" target="EGFJhHhph9IvvOz9PGOY-14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="290" y="415" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-101" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="EGFJhHhph9IvvOz9PGOY-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="290" y="180" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-102" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="709" as="sourcePoint" />
            <mxPoint x="290" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-104" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.822;entryY=-0.025;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-27" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="428.05999999999995" y="679" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-105" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="EGFJhHhph9IvvOz9PGOY-27" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="611" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-108" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-27" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="560" y="561" />
            </Array>
            <mxPoint x="638" y="561" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-109" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.65;entryY=0.975;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="EGFJhHhph9IvvOz9PGOY-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="710" y="667" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-114" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="EGFJhHhph9IvvOz9PGOY-35" target="EGFJhHhph9IvvOz9PGOY-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-117" value="Replica&lt;div&gt;DB&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1110" y="150" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-118" value="Reporting&lt;div&gt;API&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1090" y="248" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-119" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-118" target="EGFJhHhph9IvvOz9PGOY-117" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-120" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-1" target="EGFJhHhph9IvvOz9PGOY-117" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-121" value="AWS&lt;div&gt;Cloud Watch&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1123.5" y="415" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-122" value="Notification Lamba:&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="1130" y="610" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-124" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" parent="1" vertex="1">
          <mxGeometry x="1198.5" y="622" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-125" value="Cloud&lt;div&gt;Watch&lt;/div&gt;&lt;div&gt;Alarms&lt;/div&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;" parent="1" vertex="1">
          <mxGeometry x="1155.5" y="513.5" width="104.5" height="66.5" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-126" value="All Services, Modules, Lambdas, Taks&lt;div&gt;&lt;div&gt;log to AWS cloudwatch&lt;/div&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1080" y="360" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-129" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0;entryDx=25;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-121" target="EGFJhHhph9IvvOz9PGOY-125" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-130" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;dashPattern=12 12;" parent="1" source="EGFJhHhph9IvvOz9PGOY-35" target="EGFJhHhph9IvvOz9PGOY-121" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-132" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=12 12;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="125" as="sourcePoint" />
            <mxPoint x="1220" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-134" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.608;entryY=-0.017;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EGFJhHhph9IvvOz9PGOY-125" target="EGFJhHhph9IvvOz9PGOY-122" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-135" value="AWS VPC" style="swimlane;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 4;strokeWidth=3;" parent="1" vertex="1">
          <mxGeometry x="234" y="48" width="1066" height="762" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-136" value="Not for initial iteration" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1160" y="920" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="EGFJhHhph9IvvOz9PGOY-137" value="Third Party" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1160" y="958" width="140" height="22" as="geometry" />
        </mxCell>
        <mxCell id="MRXS_BM7e39pnjLLef4z-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="EGFJhHhph9IvvOz9PGOY-35" target="EGFJhHhph9IvvOz9PGOY-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
