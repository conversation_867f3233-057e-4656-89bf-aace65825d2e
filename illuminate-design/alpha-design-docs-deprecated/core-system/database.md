# Database

Standardize on Postgres

Database change management will use liquibase

Rather than inventing a way to embed liquibase into lambda deployments we will manage our set of
database as a group within a combined deployment pipeline. This still gives us database change
management which is the primary goal.

When developing we should work out database changes first. Database changes should be made backwards
compatible whenever possible. If we PR the db changes in isolation to the repo as the first commit. 
That PR will execute the current code and tests but against the new database schema so we should get
test failures if the change was not backwards compatible.
