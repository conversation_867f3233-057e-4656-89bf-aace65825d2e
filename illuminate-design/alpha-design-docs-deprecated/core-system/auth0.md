# Auth0

We will use Auth0 for Authentication.

### Users

CSA users MUST be via SSO, linked to Azure AD/Microsoft Entra.

Customer users will initially be just a database of users, but conceptually
we want to eventually offer the end user admin the ability to configure SSO
for their users as a self service option.

### MFA, Passkeys

Must consider/discuss requirements for forcing MFA (or not), use of passkeys.

### Auth0 Features

Must consider custom domains, email template configuration, email from address,
login screen customization, auth0 security features.

### Initial Expected Configuration

    2 x SPA Applications: 
        facilitate 
        illuminate
    3 x APIs: 
        facilitate-api, 
        illuminate-api, 
        internal-api
    3 x Machine-2-Machine Apps:
        zeus
        hermes
        automation-tasks

### Tenants

At a minimum we need an Auth0 tenant for dev, staging & production.

Given the concept of multiple staging areas for different purposes we might need
more than one for our staging areas, although this may be manageable via naming.
We have a similar concept with preprod and production in the production AWS account.

We could have one tenant per environment, one per AWS account.

We also might need a separate tenant for illuminate than facilitate. 

### Strategy
```
AWS Accounts, Auth0 Tenants, Application & Domain names

Might not work but going to attempt the strategy of:

1 AWS account for each of: dev, staging & production
1 AWS account pairs with 1 Auth0 tenant
Support for multiple environments within a single Account/Tenant

This ends up with 3 AWS accounts
csa-sparta-development (exists),
csa-sparta-staging, (does not yet exist)
csa-sparta-production (does not yet exist)

And four Auth0 tenants (the 4th for local development of Auth)
csa-sparta-alphadev
csa-sparta-development
csa-sparta-staging
csa-sparta-production

Production application urls
facilitate.csa.limited
illuminate.csa.limited

Proposed matching custom domain for Auth0
application-auth.csa.limited

Then the other Auth0 accounts get custom domains
application-auth-alphadev.csa.limited
application-auth-development.csa.limited
application-auth-staging.csa.limited

Then for each environment we have
facilitate-development.csa.limited
illuminate-development.csa.limited
or
facilitate-qa.csa.limited
illuminate-qa.csa.limited

Environment tags are: development, qa, uat, pentest, preprod, prod

Do we have a wildcard *.csa.limited cert for use to terminate SSL on ELB?
Alternatively do we need (do you want) explicit server certs for each application url?
or given routing via cloudflare.... we could have the csa.limited cert in cloudflare
and use a different domain and wildcard on AWS ELB?
```
