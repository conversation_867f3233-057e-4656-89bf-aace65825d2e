# Modular System

See notes in arch overview page first.

## Additional Notes

```
modular system
    micro-services-modules-and-elements:
        Business sells 'Products & Services'
        Sparta therefore has Modules
        Modules may consist of several elements 
        Features within a module may cross multiple elements
        All modules are loosely coupled with strong division between them
        This aligns to potential future migration of modules into a strong microservice design without 
        [Micro service, module, component, element, segment, division, feature]
        - true microservice overkill for small team, but strong modular system boundaries so we could
          split things up later if the need arises. We keep conceptual "services" along our module boundary with modules owing own data.
          Zeus modules: library code running in Zeus web server
          Integration modules: set of lambda behind API Gateway
          UI module: a module running in the UI
          DB module: schema owned by that service
          A given service could require a module in one or more of the locations: 
            DB, Lambdas, Web Server, UI.
            "xyz Integration Module"
                Database Element
                Serverless Element
                Web Server Element
                UI Element
          Our web server only ever talks to our SQS queues or our internal API gateway. Never anything more so we can mock out dependencies at this point.
          The intergration modules which initially are lambda could equally be standard web app packed as lambda via lambda-web-adpater or turned into permanent running ECS service behind ELB connected to from API Gateway.

    modules vs services: we keep these concept separate, every service is going to want it's own module but we may have modules that are not services. Services being things the company offers to customers and which we can provision for a customer. Some modules may just be internal to facilitate and never visible to customer.

    modules: don't know about users, can know about companies or if a company has a given service
    module data ownership
    cross module communication
    module registration
        We don't have modules register themselves. We could but it is overkill in the short term.
        The definition of services and their permissions are dbChanges items for Zeus.
    module decommissioning

    Invocations of any of our lamdbas always goes via API Gateway as this will allow us the flexibility of turning one or more lambdas into a permanently running docker container on ECS as the target behind the API gateway without changing anything else in the system.

```
