# ARM Container Images

## Github Runner

GitHub ARM workflow runners are in beta

https://github.blog/changelog/2023-10-30-accelerate-your-ci-cd-with-arm-based-hosted-runners-in-github-actions/

Once they leave beta and become mainstream then it will be easy to take advantage. Check later in 2024.

We could host our own arm runner, but then we're managing EC2 (or some other) instance and paying for that
offsetting the gains from running on arm.

## Cross Platform Builds

It is possible to create a workflow that builds a cross-platform image, but it is not without complications.

Complications come from the behaviour of docker on the runner being different, causing a different workflow
to be built for github than run locally. The built image isn't automatically in the local image store
forcing the use of a build and push atomic operation that does build and upload the image but we really
want to test the image before we push. Additionally the actual image pushed using github ends up with
multiple items in the ECR repo (possibly layers) but building the same multi arch image locally and
pushing results in only one item in ECR.

In summary whilst it should be possible the time cost of solution is not in keeping with cost saving it would
achieve, so we will just build intel images in github until the support for arm runners is out of beta. 
