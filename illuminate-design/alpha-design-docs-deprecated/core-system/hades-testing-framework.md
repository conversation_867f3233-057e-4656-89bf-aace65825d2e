# Hades Testing Framework

To be designed in more detail...

Essentially a system that can be used by each module in order to start up set of containers that may include
one or more of
* A Database
* A mock SaaS offering
* LocalStack instances of SQS queues
* The code to be tested (eg Zeus server)

Then a set of tests will get run making API calls to the module.

Hades may be run in different modes depending on the module. When testing a Zeus web server module the
tests should be making web requests to API endpoints. When testing serverless it has yet to be determined
if we mock out API gateway and still make web requests of if we invoke lambdas directly.

It may be more in keeping with the concept of possibly re-locating code behind the API gateway if we still
make web api calls to test our serverless components. Perhaps using LocalStack's API Gateway support.
