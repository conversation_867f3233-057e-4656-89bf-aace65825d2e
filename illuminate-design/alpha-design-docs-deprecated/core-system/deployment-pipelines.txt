Deployment Pipeline Notes


== General Process
    2 repos with PR release flow
    We want to only build once and push same image each env,
    We only have PRs that we can require approval.
    With only 1 repo the PR approval would create a new commit and we don't want to rebuild.
    We also can't just have images as 'latest' as trunk development needs to carry on and could be delayed before push to prod.

Hence something like.

[trunk/main] -> push -----> [Build as Vx-y-z, upload] push version.txt -> [deploy-repo] -> deploy dev
            |                                                                              auto test in situ
            |                                                                              shutdown dev
   ^        |                                                                              auto deploy qa (timer to x)
   ^        |                                                                              |
   ^-- release-please PR                                                                   |
                                                                                           PR 'Deploy to UAT/PreProd'

== PAT Token

solution requires a PAT token so that main repo can update the -deployments repo.
Value stored as secret in main repo

== Required reviewer for deployment

  That reviewers is 'any reviewer', no way to restrict list unless it is who can access -deployments.
  So we could have a "-deployments" repo for each set of things we want to restrict differently.
  In prototype just turned it to 0 reviewers for now

== Artifact Versions

    Container Versions are date-hash. Version.txt just used by release-please for humans.
    Changing the version.txt by release-please only happens after we have built and versioned the binary.
    And don't want to force a rebuild of everything on version.txt change otherwise loose ability to release
    independant changes to individual projects.

== Mono repo change detection

we don't do full build on version change, we may only want to release UI, infra change, xyz change.

== Manual deployments

we can force a release by running deployment script locally rather than in github
(assuming have aws permissions)
Alternatively: can re-run a failed workflow but could depend on why it failed.

== Adding an environment
    Add branch to -deployments
    Add workflow to -deployments
    Protect Branch in -deployments (may be already covered)
    Add
    Extra lines end of sparta/release-please to also create PR to new environment
        (The check for and the create)
    Add github permissions to AWS account to allow actions from new release branch.
    Main repo needs enviornment file name-parameters.txt
    (Perhaps env files need to be part of -deployments ?)


== Hotfix process
    Need a hotfix process
    Have to flesh out and test properly but something like

        Limit branch creation by name to admins
        git checkout Vx.y.z
        git checkout -b production-hotfix
        No release-please, no use of version.txt
        Force PRs for changes: This can be done with cherry pick fixes from trunk, before PR to production-hotfix
        Build pipeline also deploys (to preprod if it exists), then prod
        Single repo pipeline
        Job Done.
        Except... No we do need the other repo as its the other repo that can deploy (AWS permissions)
        So perhaps open permissions to allow main branch to update production
or

    Branch: production-hotfix that we can create on demand
[production-hotfix] -> OnPush ----> build as 1.2.3-hotfix01-date-ver -> push to [deploy-repo:hotfix]
                                                                            -> branches hotfix-preprod
                                                                                        hotfix-prod


== Release Notes

Created by release-please
However note the PR for a given environment will only show the items since the last release
and a release might not have gone everywhere. Something that we could eventually fix but it is what it is for now.

