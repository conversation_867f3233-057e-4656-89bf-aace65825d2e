# Load Balancer

```
ELB - 
    facilitate must have rule denying access unless from selected IPs
Load Balancing
ELB-> Athena & Zeus with
Zeus doing throttling and JWT validation for each site.
If host header & path "/" redirect to /app-name/ui
if host header /app-name/api go to Zeus
if host header /otherapp-name/api go to Zeus
if host header /app-name/ui go to Athena
if host header /otherapp-name/ui go to Athena

```

Expected that CloudFlare will offer DOS/DDOS protection

Other options like hosting static content in cloud flare, using a public instance of API Gateway, embedding static content inside zeus,... All of these could be introduced later but could also introduce unexpected complications so using the above is fine.
