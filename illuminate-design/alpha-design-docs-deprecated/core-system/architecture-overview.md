# Sparta Architecture

Sparta is architected to a be a modular system gaining some of the benefits of a microservice approach
without embracing a full microservice architecture. Modules are logically distinct features that own their own
data. Communication between elements of a module or between a module and the rest of the system is supported 
via asynchronous messaging.

## High Level Overview

![](./img/sparta-arch-overview.drawio.png)

## User Authentication

User Authentication with be done via Auth0.

## Core Web Server Containers 

The core Zeus web server will be configured to run in multiple availability zones to support high availability.

Static content will initially be served for both web sites from the Athena UI server.

![](./img/elb-fargate.drawio.png)

## Modular Solution

A module is a distinct feature within the system. It could represent a service offered
to the customers or it could be some internal functionality. A module may be split into 
multiple elements including serverless, UI or web server. It is important that the module
owns its own data and has strong logical boundaries.

![](./img/module-elements-and-data.drawio.png)

### Module Elements

Note that not all modules will require all elements. Some will not make use of the supporting
features of the platform such as scheduled tasks or async messaging.

## Internal API

Where we have a serverless element to a module we make the functions it offers into a web api via 
use of AWS API Gateway. Other parts of the system interact only via this gateway. This gives us
flexibility to migrate a serverless module into a container or to run a web server framework as a lambda
(eg using lambda-web-adapter). We can redirect the gateway at different destinations if we need
to move things. API Gateway can also do JWT auth for us adding a layer of access control without needing
implementation in every module.


## Zeus Proxy Module

User requests for data belonging to a particular module will first go the Zeus server. Zeus might have
a server module for that feature that can get its own data and response directly. We also have a proxy 
subsystem within Zeus that can make synchronous requests of our internal API to fetch data.

## Scheduled Events

We use AWS Event bridge scheduler to cause scheduled events to occur. This is used no matter where 
we want the final code to execute so that we have a common execution path. The schedule definition
is considered part of the module and once deployed will cause the event bridge to generate events
on schedule sending a message to Hermes. Hermes will the either invoke a lambda via our internal API
gateway or send a message to Zeus via its event queue.

This helps with isolation and testing as each module can have the final scheduled code tested in isolation,
and we don't need to test any logic around the scheduling itself as that is handled by AWS. We 
can just invoke a lambda, send a message to a test SQS queue, call an endpoint as appropriate and 
then check behaviour.

## Asynchronous Messaging
We support asynchronous messaging to allow decoupled interaction between modules. 

Example use cases:

- Automation task module requesting individual task executions and receiving results.
- Service provision within modules that represent services sold to customers. The module would receive 
a message when a service is added to a customer.

The current design as shown in the diagram shows the general principle with Hermes acting as our
broker withn a incoming queue and a response queue to go back to Zeus. There is reasonable chance
that the solution may grow with some additional queues, modules that can send messages and a few other
items but this remains to be seen.

## Logging and Monitoring

All code, be it web server or serverless lambdas in any language will log to cloud watch. Cloud watch logs
will be monitoring with cloud watch alarms triggered for known items in the logs. The alarms will then call
into a notification lambda that can be used to send a notification be it email, teams message, ITSM ticket. 
Additional third party monitoring and alerting solutions may be added later.
