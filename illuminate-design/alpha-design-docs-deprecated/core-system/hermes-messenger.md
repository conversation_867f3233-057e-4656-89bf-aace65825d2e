# Hermes Messenger

Essentially Her<PERSON> is a lambda that receives messages from an SQS queue, makes a web api request of our internal
api and sends the result back on a different SQS queue for Zeus to pick up and pass to a web server module.

Scheduled tasks definitions are considered part of <PERSON><PERSON>. These are defined on a module basis and are
essentially just messages sent to <PERSON><PERSON>.

<PERSON><PERSON> itself can generate logs which can be monitored so we can be alerted if a scheduled task failed.

Later design work will flesh out content of messages.
