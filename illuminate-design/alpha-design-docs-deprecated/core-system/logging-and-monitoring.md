Logging and Monitoring

What about logging who does what - should just be an ID anyway  

other

- To Cloud Watch
- Stack Traces must only be for errors
- No stack traces for 'business as usual' events
- Probably need (to write) guidance on what else to log/not-log
- Prefer ability to adjust logging for selected components without a re-build, re-start would be acceptable
- Consider how to integrate with <PERSON>'s client side request identifier

Also
- Highlight.io or other client side analytics
* Need to consider data sharing issues with whatever the service is
* Investigate self hosting , this is preference

