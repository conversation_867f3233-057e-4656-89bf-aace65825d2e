# Customer External API

We expect to eventually provide a customer facing external API that allows them to query
their own data within our system.

Whilst this is an eventual goal, it is not part of the initial iterations.

Consideration for this is the design is therefore relatively light but it could be
achieved by implementing one or more of the following

```
- Zeus web module that exposes endpoints via the ELB.
- AWS API Gateway definitions that calls to Zeus which may in turn call internal APIs 
giving an access route similar to front end code which goes via zeus.
- Dedicated Container that calls to Zeus and/or internal APIs.
```  

There remain many open questions but initial focus is elsewhere. 

Probably each customer will end up with own machine-2-machine identity in Auth0 that 
is allowed to call a new external-api with 'external-api' being a defined API in Auth0.

As we need to ensure customer x can only get customer x data we probably need to route
all requests via Zeus (or a similar new component) that can apply data filtering and/or
authorization controls not present in our internal API.

Requires a bit of thought for how we keep our modular approach with decoupled modules,
as the external api may be able to fetch data relating to multiple services. We probably
need an approach similar to how we have other elements within a module but then we add
an external api element.

So file system wise we add

```
/external-api
    /modules
        /omega-product
            somecode, yaml, swagger, etc
```

Actually they could be zeus web server modules or we could group into own server.
Could start with unique server (nest.js) following module style we have for zeus
but package with lambda-web-adapter to run serverless with API gateway in front doing
throttling and JWT auth. Then low runtime costs.
