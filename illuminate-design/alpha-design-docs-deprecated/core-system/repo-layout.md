# Sparta Git Repo

Initial aim is for a mono repo, which we can always split up later if needed.

### Proposed File system layout

```
/
    /docs
        /design-docs
            /core
            /modules
                /omega-product
                /super-secure-product
    /auth0
    /database
        /core
        /modules
            /automation-tasks
            /interesting-things
            /omega-product
            /super-secure-product
            /bobs-essential-saas            
    /zeus-server
        /core
        /modules
            /automation-tasks
    /hermes-messenger
        /scheduled-tasks
    /serverless 
        /modules
          /automation-tasks
            /interesting-things
            /omega-product
            /super-secure-product
            /bobs-essential-saas
    /hades-testing      
    /athena-ui
        /athena-components
        /modules
          /generic-module
            /interesting-things
            /omega-product
            /automation-tasks
        /front-ends
            /illuminate-ui
            /facilitate-ui

Probable additional folders
    /config
    /infrastructure
        /load-balancer
    /deployment-scripts
```

Things to find a home: docker config, local stack,...

? coding standards could be kept in own repo or include in the above as own folder.
