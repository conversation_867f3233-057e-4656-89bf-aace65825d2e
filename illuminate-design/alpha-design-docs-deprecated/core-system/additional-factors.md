# Additional Factors

Other items to factor in, after prototyping

Common Data Patterns
-	Common Data Retrieval: Sorting, Paging, Filtering
     Especially for tables...

How does this fit with modules and/or dynamic UI ?

? Notifications

No requirement for notifications as yet but if we could allow all modules
to send messages to common queues. A queue could link to sending a notification, 
or link to creating a Halo ticket for something....

### Compute Saving
- Compute Saving: (Not for day 1 but after SCSS fully over)
```
- Checkout AWS: Compute Savings Plan for Amazon ECS & Amazon EKS
  "Take advantage of Savings Plans if you have a consistent amount of Fargate usage.
  Savings Plans offer savings of up to 50% on your AWS Fargate usage in exchange for
  a commitment to use a specific amount of compute (measure in dollars per hour)
  for a one- or three-year term.
```
