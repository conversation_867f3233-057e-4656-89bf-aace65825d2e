# Front End

Need some prototyping design work and then revise this doc. 

```

front-end
    Architexture - to be fleshed out
    dynamic-content - concept of dynamic UI based on data from the server

    === Client Side ===
  - Angular
  - Angular Material (probably)
  - Font Awesome
  - Dark Mode?
  - Good chart/graph/reporting options ? ag-grid ?
  - Potentially ngRx
  ? All UI elements get ids, to support selenium, etc
  ? unauthorized modification detection
  ? Hardening of client side: encrypting endpoint names, bot detection, etc

  ? Possible use of web socket for push notifications.

    -: Telemetry Solution
      https://www.highlight.io/
      https://github.com/highlight/highlight
      ? What Alternatives.

=== Dynamic UI ?
  ? Could feed UI code config info from servers
  UI would have a mix of explicit bespoke pages with known endpoints
  and dynamically generated sections, eg for a given integrated service where
  it is told the service provides three widgets and a table and where to get data
  for these items from, it then adds a menu item which display a page with
  the widgets and table in it...
  Something like that.

```
