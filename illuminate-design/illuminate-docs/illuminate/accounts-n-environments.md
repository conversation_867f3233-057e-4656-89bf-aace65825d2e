AWS

===== AWS Accounts & Environments Setup

Environments
Dev (auto deploy)
UAT
Production (Gated Approvals)

AWS Accounts
csa-sparta-development,
csa-sparta-staging,
csa-sparta-production

Auth0 Accounts
csa-sparta-localdev
csa-sparta-development
csa-sparta-staging
csa-sparta-production

with custom domains
auth.csa.limited => for csa-sparta-production
auth-staging.csa.limited => for csa-sparta-staging
auth-development.csa.limited => for csa-sparta-development
auth-localdev.csa.limited => for csa-sparta-localdev

AWS Dev Account:
10.200.0.0/16 for a VPC in the csa-sparta-dev
10.210/16 - staging
10.220/16 - production


Environments

Local Dev - Developer Laptop

Dev - Automated deployments & automated tests, early warning of issues.
    Has to use mocks

UAT - "Manual QA" (lol), PO/Stakeholder test/demo
    :- User Testing: Connect to 'dev' sccs, but Cerberus mock

"PenTest" - for pen testing of Sparta/Illuminate

PreProd - Clone Prod data, readonly access to services

Prod - The real deal

