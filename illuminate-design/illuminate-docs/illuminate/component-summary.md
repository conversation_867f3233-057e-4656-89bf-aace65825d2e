Athena - UI

Zeus - Core platform
    "Platform" that 'hosts' service modules.
    Zeus concerned with: Companies, Users, Service Provision, Permissions
    But not the services themselves.
    - users,companies,service provision, proxy to other services for service data
    - feature flags so can turn off services as required 

Hermes - Integration Messenger/API
    - Most (all?) service data is available on a per company basis,
        No need to know who the caller is.
    - Could have a read-only mode, for use in PreProd
    - Data retrieved from <PERSON><PERSON> should be in our standards 

Perseus - PenTest Facade
    - Probably needs the JWT token passing on so we know who is making the call

Cerberus - Mock Server - Can be used to mock any web api, 
           including mocking Hermes/Perseus
           data from Cerberus reflects the data the web api is going to return

Hades - Testing Framework

