Permission System

    Probably as simple as
    Users -> [oneToMany] -> Roles -> [ManyToMany] -> Permission
    With some kind of permission interceptor on server
    @Permission('...')
    That throws if no such permission

    Question on data visibility for parent-child relationships

    But what requirements exist for the UI?

    * CyberDash Security Model
        Is it    
        ? All users of a company can see all data of that company?
    YES but this will change
    Examples:
        We need it so that we can have
            Parent->Child relations
            Users within a company can see only service x out of the services that company has
            ? there may be parts of a service that has permissions/restrictions
            ? Customer can allow partner to see it's services
            parent vs partner company
                - see also cyber dash/plat differences
            CSA != parent/partner ... or well we are more a parent            


---//

A parent can see child companies, it is 'in charge'
A partner collaborates and must be granted permission

A company has services
PenTest style grant permission to see X
We actually need to grant visibility of service X on company Y, not necessarily all services


Given a url like /companies/x/service/y/some-data
We can check user has visibility of x/y before proxy call to get data.

Services could also define a set of service permissions like 'read_x', 'set_x' and any given url could also check
the user had that permission in order to call the given url.
These could also be passed to the UI so the UI would know if to offer said option based on users 'permission set'

? filtering hermes data based on a property (or permission) of the user ?
Could do something more complicated but also could have 2 URLs "/limited" & "/full" with you need a permission for one
of them so UI will only try if that permission is present

Ignoring complications of "how to model"
UI would work a bit like surecloud with additions
    - 'csa admin' = see everything, however we do it a role that gets to see everything
    ? Do we want to have to grant everything to everyone all the time?

Hmm
Put it another way
    - There will be some kind of permission system that define
        Can User X see company Z
        Can user X see service Y on company Z
    - This will dictate both
        - Does the UI offer that company as an option
        - Does the UI show that service tab/menu option
        - Does Zeus allow calls to the endpoints for that service/company for that user.
    - Users may also have 


? Is it ok that if user X has permission to do 'operation Y' for service Z
    Then they can do that for all instances of service Z that they can see
? Or Do we need (however we get there) for instance user can 'delete agent' from service X on company Z, but only read service X on company Z2

But mostly this comes to
    Some kind of permission system
    Permission set for the user given to the UI which restricts options shown
    Zeus checks permissions on any endpoint called, 

    Filtering could be done by calling different URL or we could have support for module code, not just generic proxy in Zeus
    but beest if we can keep zeus generic, perhaps 

    Surecloud/Perseus may be more complicated but ultimately can probably be mapped ok but may need review all the special options
    in surecloud



In Summary
    - Role based access controls (not user)
    - SCCS like 'assign permission' to view company to user, but also within that company, permisisson to view services a or b
    - parent -> child company relations, possibly company -> partner relations as well.
    - admin and/or take action permissions distinct from just read access
