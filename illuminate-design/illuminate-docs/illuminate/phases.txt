PHASES

Illuminate Phases

Phase 0 :
    Training, Design, Evaluation  (? Tag Phase-0)

Phase 1:
    Core System Features    (tag: Phase-1-Core)

        Infrastructure notes: We only need single url and uk site for phase 1

    MCD Migration           (tag: Phase-1-MCD)



Phase 2:
    Surecloud migration
    Possible first new feature, like OpenCTI/OpenCVE integration
    Infrastructure: UK & US regional data & url support
    Customisable Dashboards with data from different services

Unidentified Phase
(Could be included in phase 2 but might not be)

- Endpoints in Illuminate that Cobalt/N8N can call.
- "Admin" functions within illuminate that call Cobalt/N8N
- Customer self service SSO configuration
- Customer facing API



TIMELINE

Design, Training, Evaluation
    DEC

JAN/FEB - 9 weeks = 45 days
Core Platform

March - UAT
April


Core    MCD     MCD
Core    MCD     MCD
SC      NewF.   Train(F/E)
SC      NewF.   NewF.
SC      DashB.  Sc
Sc      SC      Sc

Needs a Phase 2 Planning Stage




Timeline

Example Filter
is:open label:"p: Phase-1-Core","p: Phase-1-MCD","p: Phase-1B-Optional" label:_S_


Illuminate Tasks
60 in total
45 marked for Phase 1

Tiny 1
XS 7
S 20
M 15
L 2


Add expected ones
Tiny 1
XS 7
S 27
M 21
L 4

Low: 97
High 254
Avg 175
175 days with 3 people = 58 working days

