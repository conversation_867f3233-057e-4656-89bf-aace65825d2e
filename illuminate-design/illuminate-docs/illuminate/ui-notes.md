

Something Like
- Company select drop down - perhaps top left
  Company names have flag for location
- User ID to right
  <PERSON>u on user id for profile|settings|logout

?Admin item on user menu to switch to admin mode
?Admin mode allows creation of companies & assignment of companies to people
Does this work for customer admin (own users, setup SSO/API)
as well as CSA admin (including service admin)
? appliance management ?

- Services on LHS
  Main Content shows service data like MCD

- Dashboard, ... widgets per service




===================
==== Future Ideas
===================

: Global Error Handler
https://medium.com/@keithstric/global-angular-error-handling-8569fe8228f

: Network Cache
Similar to COPS, to avoid repeating calls for static data, etc.

