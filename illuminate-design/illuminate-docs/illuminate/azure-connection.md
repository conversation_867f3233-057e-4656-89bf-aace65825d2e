Current things in Azure include
  AppGuard
  BorderPoint
  OpenProject

OpenProject & Borderpoint likely to be removed
Might need to keep old cyber dashboard around for x months until BP gone properly but no big deal

Debated various interconnection options and/or running a server in Azure.

Final conclusion was to expose the AppGuard DB on a public IP which has IP restricted access and requires SSL
for communication with the DB.


Question:
AppGuard Facade server?
Then we can just mock everything as web api calls?

TODO:
create tasks once decided on if have facade or direct db connection.

eg
Task: Azure AppGuard DB Connectivity
Final conclusion was to expose the AppGuard DB on a public IP which has IP restricted access and requires SSL
for communication with the DB.
(Dependency on knowing outbound IPs of production sparta AWS)
