My notes from when we were about to launch facilitate

may or may not be relevant for illuminate but saving as a reminder.


===================
== : Prepare AWS production rollout
: Review database cloud formation, consider database/stack deletion prevention
  (There is value in Cloud formation yaml and option to set in GUI after stack made)
  Check also automated rolling backups, DB insights usage, and anything else can think of
  Consider KMS Id and encryption
  indexes?


===================
=== Launch Process
(Reminders for prod setup/ todo items)
===================

:AWS Prod setup
  create
    production bastion
    production database (see aws/notes.txt)
    create database tunnel script for prod (5482)
    Add to pg_admin
    Create prod_sparta database
    swap DBs (to prod_sparta) before then running
      connect & upload initial SQL
  upload db secret to system param store
  enable prod deployments
  [*.app.aws cert is already in place]

