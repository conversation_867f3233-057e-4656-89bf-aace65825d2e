Entities, DAOs, Models, DTOs, ViewModels

Lots of web projects have objects with one or more of these terms.
Whilst some specifics of some are not argued, there is not 100% consistency in how they are used across the industry.

What matters is how we are using in Illuminate.

For reference
Entity - The "thing" we are storing in the database. Often this is an DAO.
DTO = Data Transfer Object - strictly should be a POJO data container without logic
DAO = Data Access Object - provides
Model = Sometimes may be a 'domain object', or in some systems may refer to a DAO object
ViewModel = Data & logic specific for a view, some systems make this distinct from the model, google MVVM

Some systems have a lot of translation happening between different object types.

see Also
https://en.wikipedia.org/wiki/Data_access_object
https://en.wikipedia.org/wiki/Data_transfer_object
https://en.wikipedia.org/wiki/Model%E2%80%93view%E2%80%93viewmodel
https://en.wikipedia.org/wiki/Domain_model


For Illuminate code

see sparta-alpha for examples;

At the database side
We are defining types for the fields in a table alongside a type that represents a row in the table.
We then define an entity as a row in the table with all it's associations to other entities in other tables.
A 'xxPartial' is an entity where all fields are defined as optional.

We then have a repository class that wraps all actual db interaction that will return data conforming to our dao types.
Often an entity, sometimes a 'row' object.

As it stands the service layer is mostly manipulating DAO types and returning them to the controller.

The controller is mapping the DAO types into models. This is because I originally had the models just as DTOs for the client.
It may be the case that we need to move the mapping into the service, to allow the services to manipulate models.

The models are more than just DTOs. They are allowed logic. They include validation logic which can be used both client
side and server side. This means that for an incoming request we can validate the data on the server in the exact same
way that it should have been validated client side. For some commands (eg allocate to this id, or get data x) we may need
additional validation on the server but the field level validation is covered.

Terminology wise
A "model" is a representation of data, separated from any persistence concerns/layer.
A model may extend or include other 'xxxData' classes that are just abstract collections of fields to allow re-use.

An "xxxCommand" object is defined in our model layer and represents a command/request the client can send to the server.
Sometimes the client may send us a model object, eg to create or update endpoint, but other times they will send a
command, eg to a POST endpoint in order to take some other kind of action.


PageModel

Most of the time we are fine just returning a model object to the client that represents an entity but we could invent PageModels,
where we provide a set of data, not just a single entity, eg

    CustomerPageModel {
        company: CompanyModel
        products: PdxxModel

        getX(): boolean {
            examine model
        }
    }


Network package content

Some of the network responses in the alpha code got repetitive with references to some data, eg the mega 'get full company'
Rather than returning json with duplicated objects. we could perhaps return set of objects;

        company: {}
        salesforceAccount: {
            ... opportnities, lineitems,.... with reference to products
        }
        related: {
            products: {
                a: {}
                b: {}
            }
        }
  This avoids duplicating product info in the tree but would mean we need a way in the UI to grab related data.



DAO Object Rules

    (not exported) xxxFields
    with anything required for an insert being mandatory in xxxFields
    xxxRow = required version of xxFields
    entity = extend ROW with relations where relations are xx?: type = xx | null
    Entities have relations to other entities
    and a xxxPartial which gives us a flexible response at cost of enforced checks

    Note Partials have relations to entities by default, but don't have to have fetch those entities.

    For specific cases we could if we wanted override a partial
        eg
```
export interface CompanyContactEntity extends CompanyContactRow {
    company?: CompanyEntity | null
}

export interface CompanyContactPartial extends Partial<CompanyContactRow> {
    company?: CompanyPartial | null
}
```

    MOST of the time: we either return a Row or Entity
    If we need something more specialised if just getting the data and passing to a mapper, using a partial is ok
    If doing more work server side with the object, probably worth making a unique type for the data.





