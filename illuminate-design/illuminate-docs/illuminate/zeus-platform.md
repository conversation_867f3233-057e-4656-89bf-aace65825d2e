Zeus

"Platform" that 'hosts' service modules.
Zeus concerned with: Companies, Users, Service Provision, Permissions
But not the services themselves.

Authentication
Auth0
Note Auth0 will give us the user identity, but we will also have a representation of the user inside Zeus DB.
We might have an auto-create option for selected companies if Auth<PERSON> authenticates an "unknown" user.

User(s) Table
As user is a reserved word in Postgres and we are going singular table names we must choose between
"person" (like sccs), or user_account, or app_user, or even zeus_user, or some other name.
Possibly 'zeus_account'/'app_account'/'account' might be able to cover user accounts and machine accounts ?


Auth0 Integration functions:
- For creating (non SSO) users
- For setting up (self service) SSO for companies
- For setting up machine-2-machine API access accounts

Event Subsystem
- Send events for company created, service provision, etc
Possibly SNS -> SQS Fan Out
Possibly Kafka

Abstract Services
Zeus MUST 'know' it has services and what services it has but they MUST remain abstract.
Much like COPS had tickets, or <PERSON><PERSON> has Projects, then Zeus has services. What <PERSON> won't know
is that the services are Qualys, SCCS PenTest, whatever. Well it will know the name of the service which amy well be Qualys but it won't know the nature of the data or widgets offered by that service.


Service Definition

Doesn't really matter if we insert to DB, or read blobs from file system.
Except if we need to consider URL or other differences per environment.
Could have a service file.
```json
{
    serviceName: "alpha",
    serviceLocation: "hermes.local",
    dataEndpoints: {
        zeusEndpoint: "alpha",
        serviceEndpoint: "alpha",
        permissions: []
    },
    serviceProvisionData: [
        { propertyName: "Tag",
          propetyType: String
        }
    ]
}
```

Service Provision
When we allocate a service to a company any 'provision data' defined in the service must be filled in the UI for that service to be provided.

Uncertain yet if elements of the provision data need to be in URL or payload of a request to the service. One of the two, but how do we refer to them?

Also need to deal with concept that not all services in all regions. Ultimately this just means flagging the services
appropriately when defined and then passing that to UI (plus server side checks on submitted data)



Service Admin

A service may have admin requirements for which selected CSA staff have to deal with. Prime example is adding appliances to surecloud. You could look UI wise as this is something added
to a company, or you could look at is as admin of the 'pen test' service.

Also think of Jira and entering admin mode where the admin can adjust things about the Jira server such as where email goes, but also then the configuration of specific projects within Jira. Roughly equivalent to Zeus where Zeus has services rather than projects but we may still need admin of the service

We might have some generic properties within Zeus relating to a service, but things like adding an appliance are so specific to the PenTest service in this case that we really have to have a specific UI making calls to Zeus which is proxying to the service much like it proxies customer views.


Proxy Module
We have a proxy module within Zeus, so that a call to something like
https://zeus.env.location/api/comapny/{abc}/service/{def}/endpoint
Will check that the callee can view company {abc} and that they can view service {def}
(Similar but more detailed permissions on actions vs read endpoints)
If permitted: Zeus then looks up the endpoint. The endpoint will have some mapping to the actual service but will (generally) need some data item from the service provision data to be passed to the endpoint that identifies the company in question.
? Alternatively we could Kafka stream companies & service provision changes so that we can query just on company id and have the look up from company to required service tag done in Hermes/Perseus.

UI always talks to Zeus but zeus will proxy on service specific stuff
UI
Platform Admin (companies/users/service provision) -> Zeus Directly handle
Company Admin (add users, set up SSO, permissions) -> Zeus Directly handle
Service Admin -> Proxy module -> service
service data -> Proxy module -> service

If the upstream server (Hermes/Perseus) is not present, or they themselves return a 502/3
Then UI needs to show suitable "Couldn't retreive data just now, please try again later"






--------//
I can't find anything that will prevent the system being split along the lines we have.
Zeus platform connecting to other backend facades/systems which are only abstract concept to zeus, but we have
options should we need them.

Mitigating Options 

Zeus with separate Hermes, Perseus
Either we're good and the concept architecture works, or if we have to we could always merge Zeus, Hermes & Perseus
into one monolith if we find issues we didn't predict and can't overcome.

Permissions:
Either the final permission system within Zeus is sufficient that it can model permissions even within services
it doesn't have explicit knowledge about, and therefore it can check all authorisation of incoming calls
or if a service is so complicated that it needs its own explicit handling then that can be within the 
service admin area for that service, alongside kafka/sqs events which stream user details to that service, so it
can do its own authorisation. Either way Perseus/Hermes are fine to receive a 'this is Zeus' machine-2-machine
trust JWT as even if additional permissions inside a service they can trust Zeus to have done that Auth checks
on the original JWT and to provide the user to them.

Company/User Identities
We will eventually need one set of companies & users within the system.
This doesn't necessarily need to be the same DB column IDs, although it could be.
Most Hermes services we are entering the tag or other property that links a company to that service and passing
that tag to Hermes. Might not be true for every Hermes service but most (all?) don't actually need own copy
of zeus companies/users. Although some may well pull the 'company' concept from the end system this  
sccs has numerical PK Ids (named 'key'), we may need to add a column so we can have zeus_id in their table

Service data in company creation/edit and/or service provision
If a service requires some kind of id/tag to be associated with a company, and that can come from a list obtained
from the service. This still should be abstract for Zeus.
So... The service provision data definition needs a way of specifying that a column is sourced from an api call.
Then when that service is added Zeus can tell the UI the data field requirements and also make the api call to
get the optional values for the select.



Open Questions:
(Well plenty but key items...)

- Do we use Kafka Streams / SQS Events to duplicate company/service data or does Zeus lookup required tag to pass to Hermes?
- How do we secure service admin
- Permission system in general - but own design task/doc for that.

Choice of
    Kafta/SQS Streaming service provision data
or
    Mapping /company/abc/service/xyz/endpoint
        to http://hermes/api/company/def/data
        using a mapping definition like '/api/company/{service.data.qualystag}/data'
    so that url mapping gets parsed and Zeus pulls out 'service.data.qualystag' and looks up 'qualystag' in the
    service provision data for this service.
    and probably fails if it doesn't work.
    Ultimately probably quicker to do something like this, than incorporate Kafka but we may need Kafka to keep
    sccs backend going for phase 1 delivery so if we're doing it anyway?
