Monitoring & Alerting

    (?Argus? although no server per se)
    - Everything logs to cloud watch
    - Alarms in cloud watch with alerts to Teams/Halo/Wherever

Basic/Initial Implementation
  very much like last system, with alerts to teams.

Enhancement (still part of phase 1)
  alerts for errors can go to Halo
  Assuming N8N works as a web endpoint

Then this then means in test environments it's a call to mock endpoint that we can check for receipt of message
Probably need option for teams & "halo" endpoint so that in UAT we can see teams messages in slow time but 
also check that a endpoint
Indeed it's not too bad an idea to run that in prod, at least to start with.

Need the backup config so we still have a DLQ with email in event Halo message can't be sent. 
