Testable Scheduled Jobs

Need to be able to run a task/job on a scheduled basis.
Typically for data import processes.

We need to be able;
    Run these in the cloud on schedule
    Run immediately locally to test
    Run within automated tests, multiple times on demand to run a test

Need to consider overlapping jobs or "schedule next job x time after completes", see below.

Solution

Event Bridge Scheduler -> SQS Message
Then
    Application Code (eg Hermes), polls SQS
    Upon receipt starts job
    Message under processing (in flight) default timeout is 30seconds, so consider this.
    (Amazon SQS visibility timeout)
    Delete message after job complete

We initially just have the application (<PERSON><PERSON>) polling SQS and running a job based on message receipt.
As jobs are currently I/O bound having the main hermes instance(s) do this should be fine but we have
options (see below) if the tasks should end up cpu intensive or too much load for web server.

The run job on SQS message has a couple of advantages
    - Could inject an SQS message from cli to run on demand in production (if need arises)
    - Can send local message to test locally
    - Can run tests with asserts run after message deleted

De-coupling for future changes
As we may need to 'wire' up the solution differently as things evolve it becomes important that the solution
    - De-couple the frequency/execution management from the task itself.
      That is for us we keep Event Bridge/SQS/Poll, separate from an xyzTask class that run the actual job.
      Then if we want to get rid of the AWS bits we still have the task independent of the scheduling element.
    - De-couple the task from the execution context
      That is for us we keep the task as an xyzTask class that can rely on nest.js IoC and use an xyzRepo
      and or xyzClient objects, but which we could run inside a worker thread or application server
      or even if wanted a stand alone nest.js app inside a lambda. We have options but at the core
      it's still the same xyzTask class.



Overlapping Execution
Don't need on day 1 but need to have plan in case we need
    Options
        - Single Run task that schedules its own next message after exec of task.
        - DB table protection where if can't get lock, die
            So only one at a time but would 'miss' a go if message comes in whilst processing
        - If using SQS, don't read next message until done.
        - Event Bridge (single)-> SQS -> Lambda
            But the lambda just calls a web endpoint, logs results and schedules next job.
            Still have the is it cpu intensive process question and the block i/o thing.
            This last one probably not the solution, previous better but just an idea.

Any solution probably needs some output/alert if an instance missed.
Protecting against overlap by only scheduling next run after current task finishes is fine in concept
but would need rock solid solution exception handling to ensure a failure couldn't mean the next job
was never scheduled.
Compare to Event Bridge cron once/x will always reliable generate an SQS message, and we can have the db
table lock protection alongside an alert against "Warning found table x locked so can't start job x" log
message. That would lead to investigation and/or change of timing of event bridge. It could be the DB lock
wasn't released when job died but at least we can reliably get an alert to things not working.

Alternatives

If we run into an issue with a job being cpu intensive or there being lots of tasks adding to load
on the server overall we have options;

- Run the server (?eg Hermes) in question as a 2nd 'background tasks' docker
  Keeping 1 server serving web requests, and 1 doing all the SQS->Task execution

- Utilize node.js 'worker threads'
  This probably means running the nest.js app as standalone app or perhaps some code in main.ts
  that looks at env var and runs a task, or runs as normal web server.
  Web server then starts other instances of own code as worker threads with env var set when starting.
  see links below for more detail. If we go this way careful to ensure we get logging.
  eg at start up look for env var "RUN_TASK" and if there run the task in question, otherwise start
  in web server mode.


Rejected Alternatives
- Considered Lambda execution context but adds development complexity along with limit of 15min execution time,
the latter of which could definitely impact
- Considered separate process/server but unnecessary at this point, nothing stops us splitting out later.
- Considered nest.js @cron annotations, but this doesn't give us an easy way to run integration tests,
  only unit level tests
- Considered event bridge invoking docker container with specific env var to run a specific task and then die.
Downside to this is we'd end up with 1 docker per task which for i/o bound tasks is waste of money/compute resources
as we'd pay whilst provisioned, where as we can utilise a single docker for multiple concurrent i/o bound jobs via SQS.
- Considered event bridge invoking http endpoint, but this has 5 second timeout on the call, so no good.
- Could have a table of scheduled jobs and server reads this to know what to run when.
  Test code could set the DB table appropriate to what needs to be executed before running the test.
  This would save on SQS integration, messaging, etc but wouldn't give us a way to run on demand
  (except by manual DB manipulation which would be bad for own reasons)
  Plus creating/managing things via the DB table could be just as much work as integration with SQS.

Test Options:

Test
    Start Docker DB
    Populate Docker DB
    Start LocalStack
    Start server instance docker
    Send SQS message
    Test code polls 'messages in queue' with timeout
    on timeout test fails
    Otherwise once queue empty
    Run Asserts on state of DB

Similar test process could be done if we can instantiate the
task class with an IoC container and call the task exec directly.
This would skip the SQS bits so could be quicker if we have lots of
tests to do.

Would also need to start mock web apis / cerberus if task calls out to it.


Task Implementation:
- ECS Local Endpoints
For acquiring role
- AWS Profiles/config In/Out of docker
- Local Stack for Local SQS queues
- AWS queue reader in Typescript
  probably aws sdk although possible other lib
- Tasks Themselves



see also
links - for node.js concurrency & nest.js standalalone
https://docs.nestjs.com/standalone-applications?ref=rabbitbyte.club
https://medium.com/@pp.palinda/parallel-processing-in-nestjs-6ecdbc533e1f
https://blog.stackademic.com/nestjs-and-the-art-of-parallel-processing-a-beginners-journey-202fff649f2e
https://dev.to/zenstok/nestjs-dependency-injection-in-worker-threads-5deh
https://dev.to/danudenny/clustering-nest-js-2mj7
https://tsh.io/blog/simple-guide-concurrency-node-js/
https://nodejs.org/en/learn/asynchronous-work/dont-block-the-event-loop
https://medium.com/@manikmudholkar831995/worker-threads-multitasking-in-nodejs-6028cdf35e9d
https://medium.com/@manikmudholkar831995/debunking-common-nodejs-misconceptions-7172b41d7afe




