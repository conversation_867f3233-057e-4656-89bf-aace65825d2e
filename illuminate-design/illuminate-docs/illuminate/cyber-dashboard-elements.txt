Elements of Cyber Dashboard to convert.

Alerts
    Halo. - Convert Yes
    <PERSON><PERSON>. - No

BorderPoint - No

AppGuard - Special with DB
  probably keep for long time

VMaaS == Qualys = yes

M365 = borderpoint via M365 Logs - No

project = open project = No


==>>

This amounts to;

Halo
Qualys
AppGuard

For which we have
2 backend cron job python scripts (AppGuard & Qualys)
plus all the UI elements.

Halo
    "List Alerts" - x1
AppGuard
    "Agents"
    "Blocks"

Qualys
    Overview
    Cyber Essentials
    Agents
    Web Application

7 pages..
Plus the dashboard.


