Regional Provision - UK & US Data.

We MUST offer option to store data in UK or US, potentially EU option in the future.

The rule is that "All data at rest" must be held in the specified region.
It's ok for data to travel to be viewed.

We are also ok to have a single authentication store, so one Auth0 tenant which can be UK located is fine.

Whilst the requirement comes from the SCCS system, longer term it won't just apply to SCCS, we could have other services
that we want to locate in a specified region.

We will define the storage location on a company basis, and then all services for that company must be stored in that
location. It may be that some services we can't offer in some locations.

We can approach with two models

A: Duplicate systems with unique urls - This is current surecloud model
We'd have illuminate.csa.limited & illuminate-us.csa.limited (or similar)
Pointing at different AWS regions/ELBs/etc
Only interconnect would be data duplication in same vien as surecloud.

B: Single site with data held in different regions.
This is more the model that Auth0 has, where you log into Auth0
But can then select a tenant and the tenant has a location

That said we might end up bit hybrid of above...

We want the UI interface to be more like B, the auth0 model with a company dropdown showing location of each company
and the choice when making a company for where to create it.

However,

Open Questions:

? Do we need to offer customers a website that is explicitly us hosted?
Otherwise, browser traffic may be getting routed to UK

May need to try out and evaluate AWS Global Accelerator 

But any which way
Either
    - Dual URLs pointing at different ELBs
    - Single ELB talking to backends in 2 locations
    - Global Accelerator single URL to multiple ELBs
    - ? Does CloudFlare offer anything 

All require the same UI talking to Zeus with cross talk between zeus instances or between zeus and some other backend
components.

Regional UK/US
? Cloudflare vs AWS Global Accelerator vs single Zeus?
? CloudFront (vs Athena) ?
Whilst we could use these, going to keep it simpler to start with.

Next Iteration:
- Have Dual URLs, different ELBs
- When switch company, swap url in browser (if needed), so loads uk/us site
- Zeus could query 'other zeus' for a list of companies

But: Permissions for each company & user database!
So
Auth0 will always redirect back to URL first entered..
? could we have 'uk primary zeus' as source of user/company/permissions
So 'get company list' / 'get app init data' >> both get from primary zeus even if hit US Zeus
or Do we Kafka replicate user/company/permissions
and admin pages are uk only ? so writes always go to uk

User ID & Email can sync: profile info like firstname, lastname Must stay in country of residence
Company ID & Name can sync?
(Name doesn't have to be unique, can have multiple companies same name)

Could have company/user tables split from company/user data
Where
    user = user_id, email_address, location
    company = company_id, location
    user_data = firstname, lastname,
    roles/permissions can be duplicated

