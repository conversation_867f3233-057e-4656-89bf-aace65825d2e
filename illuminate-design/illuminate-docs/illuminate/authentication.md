Authentication

In simple form
Auth0 SSO & DB of users
JWT so server get auth0ID
Future requirements like SSO self service set up
? over if we want Zeus->Hermes JWT machine auth
Does Perseus require user JWT ?
Needs user migration



Hermes/Perseus should require a machine-2-machine JWT, only trusting Zeus


Auth0 Accounts
sccs currently has saml setup talking to
https://sccs-pentest.eu.auth0.com/login/callback?connection

Theoretically we may get away with adding a custom domain
so that going forwards all users, pentest or otherwise can use the custom domain
but those already set up with the above account don't have to change it.

Might also depend on timings of migration of each system.

Assuming the custom domain does work then we may be forced to keep our auth0 prod tenant name as: sccs-pentest
which is not ideal but does it really matter?
If the custom domain idea doesn't work with SAML (Why wouldn't it?) we might be forced to re-setup SSO users
for the new platform


Decision:
We will have new tenant, or use the sparta tenant
It's fine to force customers to re-create their SAML setup for new system

Even for user migration; don't worry about preserving password, MFA
They can receive new invite, that's fine.


? Auth0 and sccs-pentest tenant name.
Do we commit to moving customers, or accept living with the name forever?
Can customer domain hide it but not perfect.
user "Migration"
- We will just migrate by duplicting as new users in Auth0
