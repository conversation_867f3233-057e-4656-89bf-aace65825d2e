Surecloud Migration

Plenty to think about, but mostly in planning/design of phase 2.

Phase 1. New UI
    with Athena UI Code -> Zeus -> sccs-new-api (typescript) -> Existing DB
                        -> Axios HTTP -> "appliance-manager....."

x. Replace 'appliance-manager' functions into sccs-new-api

x. Redirect appliances to talk to sccs-new-api

x. Replace appliance code with new typescript based appliance

x. Migration
    Migrate DB tables to Sparta
    Point sccs-new-api at Sparta DB
    Remove old sccs AWS accounts    

The new server component codename 'Perseus'
(Greek Perseus: Hero, slayer of monsters)


Consider:

When we migrate appliance-manager, do we link servers with SQS
if so consider
- ECS Local Endpoints
  If we use SQS for local looped messaging (future proofing uServices?)

? Are we going to pass user JWT to Perseus .... probably have to whilst keeping both systems 'alive'

? Possibly need to migrate all surecloud users to Auth0?
Take care if sccs assumes if an auth0 id means not local user...




---//

== Company Data Merge
To bring all sccs companies into Zeus.
Add column zeus_id into sccs
SQL Export from sccs
insert (new records) into Zeus ...., include temp column for sccs_org_key
Then select all from Zeus where sccs_org_key != null
Then update sccs with zeus_id 
Then can drop sccs_org_key from zeus
Thereafter Perseus/sccs would have to read from Kafka and sccs becomes the "replica" (for company data)

Then if we wanted we could...
    the new column in sccs is just 'id'
    Then we do a alter table, update, etc....
    And make all the referring tables refer to the company.id
    Then we can drop organaisation.organisationKey
    And we'd have id's in sync as well.

It's a bit more complicated for users, as we also need in Auth0 but db wise something similar is possible


We are going to need either Kafka, Debezium CDC, or home grown SNS->SQS Data replication for users/companies, ? service provision
Too much of old system that we are preserving at least for now has user/company concepts.
