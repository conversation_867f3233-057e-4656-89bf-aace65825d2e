<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram name="Page-1" id="2rFlEJpPsh_2a0Bu2E1N">
    <mxGraphModel dx="1460" dy="1101" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-1" value="Zeus" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-2" target="hHyD3TDHN4UcUt6ciu0k-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-2" target="hHyD3TDHN4UcUt6ciu0k-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-2" value="Zeus" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="290" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-3" value="Athena&lt;div&gt;(UI Code)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-4" value="Athena&lt;div&gt;(UI Code Server)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-5" target="hHyD3TDHN4UcUt6ciu0k-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="520" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-5" value="Browser&lt;div&gt;(Running UI Code)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-6">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-6" value="ELB" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="430" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.583;entryY=0.933;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-6" target="hHyD3TDHN4UcUt6ciu0k-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.008;entryY=0.383;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" target="hHyD3TDHN4UcUt6ciu0k-5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="200" y="630" as="targetPoint" />
            <mxPoint x="170" y="570" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-10" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="140" y="530" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-11" value="Auth0" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="530" y="530" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-13" value="HTTP&lt;div&gt;(Auth: JWT)&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="305" y="480" width="105" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-14" target="hHyD3TDHN4UcUt6ciu0k-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-15" target="hHyD3TDHN4UcUt6ciu0k-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-15" target="hHyD3TDHN4UcUt6ciu0k-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-15" value="Hermes" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="354" y="90" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-16" value="Zeus&lt;div&gt;DB&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="180" y="200" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-17" value="Companies&lt;div&gt;Users&lt;/div&gt;&lt;div&gt;Service Provision&lt;/div&gt;&lt;div&gt;Permissions&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="60" y="210" width="110" height="70" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-1" target="hHyD3TDHN4UcUt6ciu0k-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-21" value="SaaS&lt;div&gt;(eg Qualys)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="20" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-22" value="Azure Hosted&lt;div&gt;AppGuard Database&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-23" value="Hermes&lt;div&gt;DB&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="190" y="70" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-24" value="Surecloud&lt;div&gt;DB&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
          <mxGeometry x="730" y="230" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-27" value="SureCloud&lt;div&gt;Appliance-Manager&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="707" y="330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-29" value="Service&lt;div&gt;Data&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="120" y="70" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-32" value="AWS Account" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="30" y="20" width="570" height="460" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-14" value="Perseus" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="hHyD3TDHN4UcUt6ciu0k-32">
          <mxGeometry x="430" y="220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.367;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-15" target="hHyD3TDHN4UcUt6ciu0k-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="hHyD3TDHN4UcUt6ciu0k-14" target="hHyD3TDHN4UcUt6ciu0k-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-37" value="vpc peer" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="600" y="235" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-38" value="IP restricted SSL comms" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="594" y="110" width="113" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hHyD3TDHN4UcUt6ciu0k-39" value="Public SaaS" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="610" y="20" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
