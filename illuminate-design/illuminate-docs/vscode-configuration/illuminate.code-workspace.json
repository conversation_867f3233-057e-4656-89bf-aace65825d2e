{
    "folders": [
        {
            "path": "..",
        },
    ],
    "extensions": {
        "recommendations": [
            "Angular.ng-template",
            "GitHub.vscode-pull-request-github",
            "usernamehw.errorlens",
            "johnpapa.Angular2"
        ]
    },
    "settings": {
        "githubIssues.issueBranchTitle": "develop/${user}-issue${issueNumber}-${sanitizedIssueTitle}",
        "githubPullRequests.commentExpandState": "collapseAll",
        "eslint.useFlatConfig": false,
        "eslint.debug": true,
        "explorer.autoReveal": true,
        "eslint.options": {
            "overrideConfigFile": ".eslintrc.json",
        },
        "editor.rulers": [
            80,
            125
        ],
        "workbench.colorCustomizations": {
            "editor-ruler.foreground": "#0099AA33"
        },
        "eslint.validate": [
            "javascript",
            "html",
            "typescript"
        ],
        "eslint.workingDirectories": [
            {
                "directory": "./code/ui/athena",
            },
            {
                "directory": "./code/ui/libs/athena-controls",
            },
            {
                "directory": "./code/servers/zeus"
            },
            {
                "directory": "./code/servers/zeus-server-e2e"
            },
            {
                "directory": "./code/servers/cerberus"
            },
            {
                "directory": "./code/servers/hermes"
            },
            {
                "directory": "./code/servers/hermes-e2e"
            },
            {
                "directory": "./code/servers/perseus"
            },
            {
                "directory": "./code/servers/perseus-e2e"
            },
        ],
    },
}