CREATE TABLE "qualys_agents" (
  "agent_id" int,
  "company_id" int,
  "dns_name" varchar(200) DEFAULT '',
  "netbios_name" varchar(200) DEFAULT '',
  "ip_address" varchar(50) DEFAULT null,
  "operating_system" varchar(200) DEFAULT '',
  "last_connected" datetime DEFAULT null,
  "last_vulnsUpdate" datetime DEFAULT null,
  PRIMARY KEY ("agent_id", "company_id")
);

CREATE TABLE "qualys_vulnerabilities" (
  "unique_vuln_id" int PRIMARY KEY,
  "title" varchar(200) DEFAULT null,
  "confirmation_status" int DEFAULT null,
  "severity" float NOT NULL,
  "cvss_v3" float DEFAULT null,
  "ce_plus_fail" int DEFAULT null,
  "threat" text NOT NULL,
  "impact" text NOT NULL,
  "solution" text NOT NULL
);

CREATE TABLE "qualys_agent_vulnerabilities" (
  "agent_id" int,
  "unique_vuln_id" int,
  "first_seen" datetime DEFAULT null,
  "last_seen" datetime DEFAULT null,
  "times_seen" int DEFAULT null,
  "status" varchar DEFAULT null,
  PRIMARY KEY ("agent_id", "unique_vuln_id")
);

CREATE TABLE "qualys_companies" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "name" varchar(50) NOT NULL DEFAULT '',
  "tag_id" int DEFAULT null
);

CREATE TABLE "qualys_cves" (
  "cve_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "cve_number" varchar(50) NOT NULL
);

CREATE TABLE "qualys_cves_vulnerabilities" (
  "cve_id" int,
  "unique_vuln_id" int,
  PRIMARY KEY ("cve_id", "unique_vuln_id")
);

CREATE TABLE "qualys_wa_apps" (
  "webapp_id" int PRIMARY KEY,
  "company_id" int NOT NULL,
  "name" varchar(100) DEFAULT null,
  "created_date" datetime DEFAULT null,
  "url" varchar(300) DEFAULT null,
  "operating_system" varchar(300) DEFAULT null
);

CREATE TABLE "qualys_wa_scans" (
  "webapp_id" int,
  "scan_id" int,
  "name" varchar(100) DEFAULT null,
  "start_time" datetime DEFAULT null,
  "end_time" datetime DEFAULT null,
  "type" varchar(100) DEFAULT null,
  "status" varchar(100) DEFAULT null,
  PRIMARY KEY ("webapp_id", "scan_id")
);

CREATE TABLE "qualys_wa_vuln_instances" (
  "unique_id" INT GENERATED BY DEFAULT AS IDENTITY,
  "scan_id" int,
  "vuln_id" int,
  "url" text DEFAULT null,
  "payloads" json DEFAULT null,
  "finding_id" int DEFAULT null,
  PRIMARY KEY ("unique_id", "scan_id", "vuln_id")
);

CREATE TABLE "qualys_wa_vulnerabilities" (
  "vuln_id" int PRIMARY KEY,
  "title" varchar(300) DEFAULT null,
  "threat" text DEFAULT null,
  "impact" text DEFAULT null,
  "solution" text DEFAULT null,
  "severity" float DEFAULT null,
  "potential" int DEFAULT null,
  "cvss_v3" float DEFAULT null
);

CREATE TABLE "appguard_companies" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "name" varchar(100)
);

CREATE TABLE "appguard_groups" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "company_id" int,
  "name" varchar(100),
  "is_vdi" int,
  "operating_system" varchar(300)
);

CREATE TABLE "appguard_agents" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "group_id" int,
  "name" varchar(100),
  "is_active" int,
  "last_online" datetime
);

CREATE TABLE "appguard_licenses" (
  "id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "company_id" int,
  "expiry_date" datetime,
  "total_licenses" int,
  "workstation_licenses" int,
  "server_licenses" int,
  "linux_licenses" int
);

ALTER TABLE "qualys_agents" ADD FOREIGN KEY ("agent_id") REFERENCES "qualys_agent_vulnerabilities" ("agent_id");

ALTER TABLE "qualys_vulnerabilities" ADD FOREIGN KEY ("unique_vuln_id") REFERENCES "qualys_agent_vulnerabilities" ("unique_vuln_id");

ALTER TABLE "qualys_agents" ADD FOREIGN KEY ("company_id") REFERENCES "qualys_companies" ("id");

ALTER TABLE "qualys_cves_vulnerabilities" ADD FOREIGN KEY ("cve_id") REFERENCES "qualys_cves" ("cve_id");

ALTER TABLE "qualys_cves_vulnerabilities" ADD FOREIGN KEY ("unique_vuln_id") REFERENCES "qualys_vulnerabilities" ("unique_vuln_id");

ALTER TABLE "qualys_wa_apps" ADD FOREIGN KEY ("company_id") REFERENCES "qualys_companies" ("id");

ALTER TABLE "qualys_wa_scans" ADD FOREIGN KEY ("webapp_id") REFERENCES "qualys_wa_apps" ("webapp_id");

ALTER TABLE "qualys_wa_vuln_instances" ADD FOREIGN KEY ("scan_id") REFERENCES "qualys_wa_scans" ("scan_id");

ALTER TABLE "qualys_wa_vuln_instances" ADD FOREIGN KEY ("vuln_id") REFERENCES "qualys_wa_vulnerabilities" ("vuln_id");

ALTER TABLE "appguard_licenses" ADD FOREIGN KEY ("company_id") REFERENCES "appguard_companies" ("id");

ALTER TABLE "appguard_groups" ADD FOREIGN KEY ("company_id") REFERENCES "appguard_companies" ("id");

ALTER TABLE "appguard_agents" ADD FOREIGN KEY ("group_id") REFERENCES "appguard_groups" ("id");
