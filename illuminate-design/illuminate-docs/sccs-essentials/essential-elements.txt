
Surecloud Essential Elements

- Authentication including company SSO
- Company Management (CRUD: Create Update Delete)
- User Management (CRUD: Create Update Delete)

- UI: Swap between different company views
- The 'allocate' access to company x for y days for reason z

- Activity Logs ?
    + When someone gets access it shows in the log for that company
    + See that someone has logged in
    + [Not there today] like: a 'user x viewed company y' (csa admin or customer admin)


[Scanning: Either Port
    - Appliances
        Create, view status, files, running tasks, etc
    - Appliance scanning
    - Jobs in Progress
    - Scheduled (appliance) jobs, including set up by customers
    - Admin? Tool policies/settings
or
    Replace with Qualys
    but not just a 'upload qualys' results, but a more integrated solution
]

- Assets (define, update, etc) - (could do with improving what it does)
- Security Testing Assessments
        including all the reports, finalised reports, etc
- Vulnerability Management (unless we do something with Qualys)
        including pdf gen.
- Reference (view/edit vulnerability data)

- Historic Data
raw vuln, data can become stale, eg 3 years old
but if within a pen.test/assessement
: Have to keep within reason, although really really old might be able to go

- User Email Notifications, for task assignment, etc

Useful, but not necessarily essential
- System Notifications: that banner on login thing

- Dashboards (nice ofc but is it essential ?)
    Something is needed.
    Doesn't have to be all singing configuable chooser your "portlet" & layout
    see cyber security sandbox on prod instance
    "Security Testing" dashboard
    Best if we give them a known setup, perhaps with some limited options
    like slice by workstations,
    vulnerability that are critical
    ? Perhaps some compoents on "the dashboard" & an overview "page" inside the service menu
    Some work to define precise requirements

- Portlet Links
When create dashboard, can get a long url, that returns data behind the portlet
Nick - pulls data to push into Jira
Customers may or may not but won't use heavily.

- The 'custom data' URLs
    (Customers: "new gate", "advanced", others ?
  Wouldn't need to replicate whilst the old system is still up.
  certainly could remove if replaced with our api as an interface.
  Ideally before we turn off old system we give them a way to get via another way.


Dead Concepts

- PCI Scans
- Event Management
- Wireless

To Die
- Tasks
    (in theory should be able to die, some clients use but can be managed
     Best to pull some stats about use, and by whom before final decision)
     Possible alternatives with email notifications could be invented if needed.

- The 'search platform' box (was more for GRC)

- User 'Groups'
