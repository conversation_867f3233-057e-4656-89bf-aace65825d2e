name: status-check

# This is a generic workflow that can be put in PR status checks which
# will wait for any triggered workflow tasks to complete.
# see
# https://github.com/poseidon/wait-for-status-checks

on:
  pull_request:
    branches: [ "main" ]

jobs:
  status-check:
    runs-on: ubuntu-latest
    permissions:
      checks: read
    steps:
      - name: GitHub Checks
        uses: poseidon/wait-for-status-checks@v0.4.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
