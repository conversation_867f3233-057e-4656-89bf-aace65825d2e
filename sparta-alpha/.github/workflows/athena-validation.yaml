name: Athena PR Validation

on:
  pull_request:
    branches: [ "main" ]
    paths: ['athena/**/*', 'common-libs/**/*', '.github/workflows/athena-validation.yaml']

  workflow_dispatch:

jobs:

  athena-validation:

    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: top level npm
        run: |
          npm install -g pnpm
          npm install -g nx

      - name: Build the Docker image.
        run: |
          npm run update-libs
          npm run build-athena-docker
