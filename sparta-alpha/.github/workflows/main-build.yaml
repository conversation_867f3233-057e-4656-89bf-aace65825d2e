name: Sparta Build

on:
  push:
    branches: [ "main" ]

    workflow_dispatch:

concurrency:
  # Have to build in order, can't allow a later commit to complete first
  # but never see it's changes delivered as the earlier commit ends up
  # live instead.
  group: ${{ github.workflow }}-${{ github.ref }}
  # Can't cancel in progress as we look for changes and a later commit
  # Might only need to build a part of the system.
  cancel-in-progress: false

permissions:
  id-token: write # This is required for requesting the JWT for AWS
  contents: write
  pull-requests: write

jobs:

  core-build:

    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          path: sparta
      - name: checkout
        uses: actions/checkout@v4
        with:
          repository: cyber-security-associates-ltd/sparta-deployments
          token: ${{ secrets.DEPLOYPAT }}
          path: deployments

      - name: Calculate version
        working-directory: sparta
        run: echo "BUILD_VERSION=$(echo $(date +%F)-$(git rev-parse --short HEAD))" >> $GITHUB_ENV
      - name: Output Version
        run: echo $BUILD_VERSION

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: top level npm
        working-directory: sparta
        run: |
          npm install -g pnpm
          npm install -g nx
          npm run update-libs

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::058264184932:role/GitHub-ecr-sparta-main
          role-session-name: aws-session
          aws-region: eu-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - uses: dorny/paths-filter@v3
        id: filter
        with:
          working-directory: sparta
          filters: |
            backend:
              - 'zeus/**'
              - 'common-libs/**'
            frontend:
              - 'athena/**'
              - 'common-libs/**'
            common:
              - 'common-libs/**'

      - name: Build (lint) common lib
        if: steps.filter.outputs.common == 'true'
        working-directory: sparta
        run: |
          npm run build-common

      - name: Build the Athena Docker image.
        if: steps.filter.outputs.frontend == 'true'
        working-directory: sparta
        run: |
          npm run build-athena-docker

      - name: tag and push Athena docker
        if: steps.filter.outputs.frontend == 'true'
        working-directory: sparta
        run: |
          docker images
          docker tag athena 058264184932.dkr.ecr.eu-west-2.amazonaws.com/sparta/athena:$BUILD_VERSION
          docker push 058264184932.dkr.ecr.eu-west-2.amazonaws.com/sparta/athena:$BUILD_VERSION

      - name: Build the Zeus Docker image.
        if: steps.filter.outputs.backend == 'true'
        working-directory: sparta
        run: |
          npm run build-zeus-docker

      - name: tag and push Zeus docker
        if: steps.filter.outputs.backend == 'true'
        working-directory: sparta
        run: |
          docker images
          docker tag zeus 058264184932.dkr.ecr.eu-west-2.amazonaws.com/sparta/zeus:$BUILD_VERSION
          docker push 058264184932.dkr.ecr.eu-west-2.amazonaws.com/sparta/zeus:$BUILD_VERSION

      - name: ensure deployments folders exists
        working-directory: deployments
        run: |
          mkdir -p aws/deploy
          mkdir -p aws/service-versions

      - name: remove old deploy scripts
        working-directory: deployments/aws
        run: |
          rm -rf deploy

      - name: update deploy scripts
        working-directory: deployments
        run: |
          cp -r ../sparta/aws/deploy aws/deploy

      - name: update build reference number
        if: steps.filter.outputs.backend == 'true'
        working-directory: deployments/aws
        run: |
          echo $BUILD_VERSION > service-versions/zeus-server-version.txt

      - name: update build reference number athena
        if: steps.filter.outputs.frontend == 'true'
        working-directory: deployments/aws
        run: |
          echo $BUILD_VERSION > service-versions/athena-ui-version.txt

      - name: Commit deployment changes
        working-directory: deployments
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Sparta Build"
          if [[ -n $(git status -s) ]]; then
            git add --all
            git commit -m "Build: $BUILD_VERSION"
            git push
          fi

  # Only run release-please after core build completes
  # So we can't get into situation where the release-pr can be completed
  # but the build hasn't finished creating the artifacts
  release-please:
    runs-on: ubuntu-latest
    needs: core-build
    outputs:
      release_created: ${{ steps.release-please.outputs.release_created }}
    steps:
      - uses: googleapis/release-please-action@v4
        id: release-please
        with:
          release-type: simple
          bump-minor-pre-major: Yes

  create-deployment-prs:
    runs-on: ubuntu-latest
    needs: release-please
    if: ${{ needs.release-please.outputs.release_created }}
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          path: sparta
      - name: checkout
        uses: actions/checkout@v4
        with:
          repository: cyber-security-associates-ltd/sparta-deployments
          token: ${{ secrets.DEPLOYPAT }}
          path: deployments
      - name: Read version string
        working-directory: sparta
        run: echo "BUILD_VERSION=$(cat version.txt)" >> $GITHUB_ENV
      - name: Output Version Check
        run: echo $BUILD_VERSION
      - name: Read release please commit
        working-directory: sparta
        run: echo "$(git log -n 1 | sed 's/^[[:space:]]*//g')" > rp-commit.txt
      - name: Branch and PR to later stages
        env:
          GITHUB_TOKEN: ${{ secrets.DEPLOYPAT }}
        working-directory: deployments
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Build Server"
          git checkout -b versions/version-$BUILD_VERSION
          git push --set-upstream origin versions/version-$BUILD_VERSION
          changes=$(cat ../sparta/rp-commit.txt)

          existingPrNumber=$(gh pr list --base release-uat --json number | jq '.[0].number')
          if [ ! $existingPrNumber = 'null' ]; then
            gh pr close $existingPrNumber
          fi
          gh pr create -B release-uat -H versions/version-$BUILD_VERSION --title "Deploy Release $BUILD_VERSION to UAT" --body "$changes"

          existingPrNumber=$(gh pr list --base release-prod --json number | jq '.[0].number')
          if [ ! $existingPrNumber = 'null' ]; then
            gh pr close $existingPrNumber
          fi
          gh pr create -B release-prod -H versions/version-$BUILD_VERSION --title "Deploy Release $BUILD_VERSION to Prod" --body "$changes"

