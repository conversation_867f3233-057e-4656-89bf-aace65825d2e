{"name": "athena-docker", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "athena/athena-docker", "projectType": "docker", "tags": [], "implicitDependencies": ["facilitate-ui", "illuminate-ui"], "targets": {"build": {"dependsOn": ["^build"], "executor": "nx:run-commands", "options": {"cwd": "", "command": "docker build -f athena/athena-docker/athena.Dockerfile -t athena ."}}}}