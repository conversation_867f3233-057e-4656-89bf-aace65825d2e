import { Injectable } from '@angular/core'
import { HttpBackend, HttpClient } from '@angular/common/http'
import { AuthClientConfig } from '@auth0/auth0-angular'
import { Observable, tap } from 'rxjs'
import { AuthConfigDto } from '@nx-sparta/data-model'

@Injectable({
    providedIn: 'root',
})
export class AuthInitService {

    public loadAuthConfig(handler: HttpBackend, config: AuthClientConfig): Observable<unknown> {
        return new HttpClient(handler)
            .get<AuthConfigDto>('/facilitate-api/auth-config').pipe(
                tap(
                (loadedConfig: AuthConfigDto) => {

                    if (!loadedConfig.domain) {
                        alert('Invalid Authentication Configuration!')
                    }

                    const interceptedRoutes = ['*', '/*']

                    /* Set the configuration with auth0 */
                    config.set({
                        domain: loadedConfig.domain,
                        clientId: loadedConfig.clientId,
                        authorizationParams: {
                            audience: loadedConfig.audience,
                            // third party object naming
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            redirect_uri: window.location.origin,
                            connection: 'facilitate-sso',
                        },
                        httpInterceptor: {
                            allowedList: interceptedRoutes,
                        },
                    })
                }))
    }
}
