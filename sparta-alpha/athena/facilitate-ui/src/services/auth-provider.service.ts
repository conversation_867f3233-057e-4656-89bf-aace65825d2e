import { Injectable } from '@angular/core'
import { AuthService } from '@auth0/auth0-angular'
import { filter, first, mergeMap } from 'rxjs/operators'
import { Router } from '@angular/router'
import { tap } from 'rxjs'
import { AcLogger } from '@nx-sparta/athena-controls'

/**
 * Provides authentication support within the client
 */
@Injectable({
    providedIn: 'root',
})
export class AuthProviderService {

    postAuthTargetUrl = ''

    broadcastChannel = new BroadcastChannel('sparta_auth')

    logoutInProgress = false

    invalidStateError = false

    constructor(
        private auth0Service: AuthService,
        private router: Router,
    ) {

        /**
         * Listens for errors from Auth0 SDK.
         * For errors that are explicitly login required we redirect to the login page,
         * Other errors will bubble up normally
         */
        this.auth0Service.error$.pipe(
            filter((e) => e.message === 'Login required'),
            mergeMap(() =>
                this.auth0Service.loginWithRedirect({
                    appState: {
                        // We set the target fragment within the app for us to return to after auth
                        appUrl: this.router.url,
                    },
                }),
            ),
        ).subscribe()

        this.auth0Service.error$.pipe(
            filter((e) => e.message === 'Invalid state'),
            tap(() =>
                this.invalidStateError = true,
            ),
        ).subscribe()

        this.auth0Service.error$.pipe(tap(e => {
            AcLogger.log('Authentication error', e)
        })).subscribe()

        this.auth0Service.appState$.subscribe(appState => {
            this.postAuthTargetUrl = appState['appUrl']
        })

        this.broadcastChannel.onmessage = (message): void => {
            if (location.origin === message.origin) {
                if (message.data.action === 'logout') {
                    this.otherPageLoggingOut(message.data.inactivity)
                }
            }
        }
    }

    startupAuthenticationCheck(ifOkCallback: () => void, redirectUrl?: string): void {
        if (redirectUrl) {
            this.postAuthTargetUrl = redirectUrl
        }

        this.auth0Service.isAuthenticated$.subscribe(isAuthenticated => {
            if (isAuthenticated) {
                ifOkCallback()
            } else {
                if (this.invalidStateError) {
                    // If we have seen an invalid state error we delay the call to log in
                    // This reduces chance of hammering server if error is persistent.
                    // We then also make a slightly different redirect call that doesn't automatically choose
                    // the facilitate-sso connection, as for some reason logging in this way has seen that
                    // same error disappear although no longer reproducible.
                    setTimeout(() => {
                        this.auth0Service.loginWithRedirect({
                            appState: {
                                fragment: redirectUrl ? redirectUrl : this.router.url,
                            },
                            authorizationParams: {
                                connection: '',
                            },
                        })
                    }, 5000)
                } else {
                    this.redirectToLoginPage(this.postAuthTargetUrl)
                }
            }
        })
    }

    // Called when we receive an unauthorised error over the network
    unauthorisedNetworkError(): void {
        this.auth0Service.isAuthenticated$.pipe(first()).subscribe(isAuthenticated => {
            if (isAuthenticated) {
                // Client believes it is authenticated but server does not.
                // so logout from auth0
                this.logout()
            } else {
                // otherwise go to login page as client thinks it is not logged in
                this.redirectToLoginPage()
            }
        })
    }

    redirectToLoginPage(location?: string): void {
        this.auth0Service.loginWithRedirect({
            appState: {
                // We set the target fragment within the app for us to return to after auth
                fragment: location ? location : this.router.url,
            },
        })
    }

    private otherPageLoggingOut(inactivity = false): void {
        // Call our internal logout function, which has a guard against
        // this happening during a call this page has already been made to log out
        this.internalLogout(inactivity)
    }

    logout(inactivity = false): void {
        this.broadcastChannel.postMessage({ action: 'logout', inactivity })
        this.internalLogout(inactivity)
    }

    private internalLogout(inactivity = false): void {
        //  Guard against hitting inactivity timeout on multiple tabs 'at the same time'
        // Strictly one will go first but multiple tabs could call their own logout before seeing a postmessage
        // from another tab/window
        if (this.logoutInProgress) return
        let postLogoutPage = window.location.origin
        if (inactivity) {
            postLogoutPage += '/Inactive.html'
        }
        this.logoutInProgress = true
        this.auth0Service.logout({logoutParams: {
            returnTo: postLogoutPage,
        }})
    }

}

