import { Component } from '@angular/core'
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router'
import { AsyncPipe } from '@angular/common'
import { AuthProviderService } from '../services/auth-provider.service'
import { FaIconComponent } from '@fortawesome/angular-fontawesome'
import { faBuilding, faChartLine, faCompassDrafting } from '@fortawesome/free-solid-svg-icons'
import { RippleModule } from 'primeng/ripple'
import { AcLogger, AcSpinnerComponent } from '@nx-sparta/athena-controls'

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [
        RouterOutlet,
        RouterOutlet,
        RouterLink,
        RouterLinkActive,
        AsyncPipe,
        FaIconComponent,
        RippleModule,
        AcSpinnerComponent,
    ],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss',
})
export class AppComponent {

    protected readonly faBuilding = faBuilding
    protected readonly faCompassDrafting = faCompassDrafting
    protected readonly faChartLine = faChartLine

    authComplete = false

    constructor(
        private authService: AuthProviderService,
        private router: Router,
    ) {
        AcLogger.log('app init')

        this.authService.startupAuthenticationCheck(() => {
            // Post start up checks...
            this.authComplete = true
        })

    }

    home(): void {
        this.router.navigateByUrl('welcome')
    }

}
