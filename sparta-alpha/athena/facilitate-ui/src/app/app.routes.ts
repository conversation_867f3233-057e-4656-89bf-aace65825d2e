import { Routes } from '@angular/router'
import { WelcomeComponent } from '../components/welcome/welcome.component'
import { CompanyListComponent } from '../components/company-list/company-list.component'
import { NewCompanyComponent } from '../components/new-company/new-company.component'
import { CompanyDetailsComponent } from '../components/company-details/company-details.component'
import { ProductListComponent } from '../components/product-list/product-list.component'
import { DashboardComponent } from '../components/dashboard/dashboard.component'
import { CanDeactivateFormGuardFn } from '@nx-sparta/athena-controls'

export const routes: Routes = [
    { path: '', redirectTo: 'welcome', pathMatch: 'full' },
    { path: 'welcome', component: WelcomeComponent },
    { path: 'company-list', component: CompanyListComponent },
    { path: 'new-company', component: NewCompanyComponent },
    { path: 'company/:companyId', component: CompanyDetailsComponent, canDeactivate: [CanDeactivateFormGuardFn] },
    { path: 'product-list', component: ProductListComponent },
    { path: 'dashboard', component: DashboardComponent },
]


