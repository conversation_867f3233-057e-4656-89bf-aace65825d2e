@import "includes/common";

.entire-page {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.page-content {
    width: 100%;
    height: 100%;
    max-width: 1920px;
    flex: 1 0 auto;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.menu-item {
    text-decoration: none;
    white-space: nowrap;
    color: black;

    .menu-text {
        font-size: 20px;
    }
}

.side-nav {
    display: inline-block;
    //border: 1px solid #eaeaea;
    //box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 7px;
    width: 170px;
    flex-grow: 0;
    height: 100%;
    margin-left: 10px;

    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        width: 100%;

        li a {
            display: block;
            border-radius: 30px;
            padding: 10px 15px;
            margin: 10px;
        }

        li a:hover {
            cursor: pointer;
            background-color: #209DD8;
            color: white;
        }
    }
}

.disabled {
    pointer-events: none;
    cursor: default;
    color: gray;
}

.main-content {
    flex: 1 0 auto;
    overflow-y: auto;
    margin: 2px 5px 0px 0px;
}

.app-logo {
    margin: 10px;
}



