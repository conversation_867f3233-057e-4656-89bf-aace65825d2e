import { APP_INITIALIZER, ApplicationConfig, provideZoneChangeDetection } from '@angular/core'
import { provideRouter } from '@angular/router'
import { HttpBackend, provideHttpClient, withInterceptors } from '@angular/common/http'

import { routes } from './app.routes'
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async'
import { AuthClientConfig, authHttpInterceptorFn, provideAuth0 } from '@auth0/auth0-angular'
import { AuthInitService } from '../services/auth-init.service'
import { Observable } from 'rxjs'

export const initApp = (handler: HttpBackend, config: AuthClientConfig, authInitService: AuthInitService) =>
    (): Observable<unknown> => authInitService.loadAuthConfig(handler, config)

export const appConfig: ApplicationConfig = {

    providers: [

        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(routes),
        provideHttpClient(withInterceptors([authHttpInterceptorFn])),
        provideAnimationsAsync(),

        {
            provide: APP_INITIALIZER,
            useFactory: initApp,
            multi: true,
            deps: [HttpBackend, AuthClientConfig, AuthInitService],
        },

        provideAuth0(),

    ],
}

