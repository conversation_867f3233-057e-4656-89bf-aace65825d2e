import { Component, OnInit } from '@angular/core'
import { Router, RouterLink, RouterLinkActive } from '@angular/router'
import { TableModule } from 'primeng/table'
import { CommonModule } from '@angular/common'
import { IconFieldModule } from 'primeng/iconfield'
import { InputIconModule } from 'primeng/inputicon'
import { Button } from 'primeng/button'
import { AcContentWrapperComponent, AcHeaderBarComponent, ButtonBarComponent, TypedHttpClientService } from '@nx-sparta/athena-controls'
import { CompanyNameModel } from '@nx-sparta/data-model'

@Component({
    selector: 'app-company-list',
    standalone: true,
    imports: [
        RouterLink,
        RouterLinkActive,
        AcHeaderBarComponent,
        AcContentWrapperComponent,
        TableModule,
        CommonModule,
        IconFieldModule,
        InputIconModule,
        Button,
        ButtonBarComponent,
    ],
    templateUrl: './company-list.component.html',
    styleUrl: './company-list.component.scss',
})
export class CompanyListComponent implements OnInit {

    companyList: CompanyNameModel[] = []

    constructor(
            private typedClient: TypedHttpClientService,
            private router: Router,
    ) {
    }

    ngOnInit(): void {
        this.typedClient.getTyped<CompanyNameModel[]>(CompanyNameModel, '/facilitate-api/companies').subscribe(
            (data: CompanyNameModel[]) => {
                this.companyList = data
            },
        )
    }

    select(id: number): void {
        this.router.navigateByUrl(`/company/${id}`)
    }

    createNew(): void {
        this.router.navigateByUrl('new-company')
    }

}
