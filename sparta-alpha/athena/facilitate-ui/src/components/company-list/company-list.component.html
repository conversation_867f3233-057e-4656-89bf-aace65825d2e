<ac-header-bar>Companies</ac-header-bar>
<ac-content-wrapper>
    <ac-button-bar justifyLeft="true">
        <p-button icon="pi pi-plus" label="Create" [rounded]="true" (onClick)="createNew()" />
    </ac-button-bar>
    <div class="company-list">
        <p-table
            #dt2
            [value]="companyList" [tableStyle]="{ 'min-width': '50rem' }"
            styleClass="p-datatable-striped"
            [globalFilterFields]="['name']"
            [paginator]="true"
            [rows]="15"
            [rowsPerPageOptions]="[10, 15, 25, 50]"
        >
            <ng-template pTemplate="header">
                <tr>
                    <th pSortableColumn="name">Company Name<p-sortIcon field="name" /></th>
                    <th pSortableColumn="isPartner">Partner<p-sortIcon field="isPartner" /></th>
                    <th pSortableColumn="isReferrer">Referrer<p-sortIcon field="isReferrer" /></th>
                    <th pSortableColumn="unallocatedOpportunity">Opportunities Allocated<p-sortIcon field="unallocatedOpportunity" /></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-company>
                <tr (click)="select(company.id)" class="company-row">
                    <td>{{ company.name }}</td>
                    <td>{{ company.isPartner }}</td>
                    <td>{{ company.isReferrer }}</td>
                    <td>
                        <i class="pi" [ngClass]="{ 'text-green-500 pi-check-circle': !company.unallocatedOpportunity, 'text-red-500 pi-times-circle': company.unallocatedOpportunity }"></i>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</ac-content-wrapper>
