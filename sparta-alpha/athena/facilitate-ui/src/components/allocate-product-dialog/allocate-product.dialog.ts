import { Component, ViewChild } from '@angular/core'
import { FormsModule, NgForm } from '@angular/forms'
import { But<PERSON> } from 'primeng/button'
import { NgSelectModule } from '@ng-select/ng-select'
import { DynamicDialogConfig, DynamicDialogModule, DynamicDialogRef } from 'primeng/dynamicdialog'
import { InputTextModule } from 'primeng/inputtext'
import { CalendarModule } from 'primeng/calendar'
import { AcClassFieldValidatorDirective, AcFormDirective, AcFormFieldComponent, ButtonBarComponent, ButtonSpacerComponent, FormUtils } from '@nx-sparta/athena-controls'
import {
    CompanyNameModel,
    ContractTerm,
    ProductAllocationCommand,
    ProductCategoryModel,
    ProductModel,
    SalesforceLineItemModel,
    SalesforceOpportunityModel,
    SubProductAllocationModel,
    SubProductGroupModel,
} from '@nx-sparta/data-model'

@Component({
    selector: 'app-allocate-product-dialog',
    standalone: true,
    imports: [
        AcFormDirective,
        AcFormFieldComponent,
        FormsModule,
        Button,
        ButtonBarComponent,
        ButtonSpacerComponent,
        NgSelectModule,
        DynamicDialogModule,
        AcClassFieldValidatorDirective,
        InputTextModule,
        CalendarModule,
        AcClassFieldValidatorDirective,
    ],
    templateUrl: './allocate-product.dialog.html',
    styleUrl: './allocate-product.dialog.scss',
})
export class AllocateProductDialog {

    protected readonly contractTermValues = Object.values(ContractTerm)
    protected readonly SubProductAllocationDto = SubProductAllocationModel

    @ViewChild('myForm') myForm?: NgForm

    data!: {
        opportunity: SalesforceOpportunityModel
        lineItem: SalesforceLineItemModel
        companyList: CompanyNameModel[]
        productList: ProductCategoryModel[]
        subProductGroups: SubProductGroupModel[]
        command: ProductAllocationCommand
        validationClass: new() => object
    }

    productSet: ProductModel[] = []

    constructor(
        private dialogRef: DynamicDialogRef,
        private config: DynamicDialogConfig,
    ) {
        this.data = config.data

        this.data.productList.forEach(productCategory => {
            if (productCategory.products) this.productSet.push(...productCategory.products)
        })
    }

    cancel(): void {
        this.dialogRef.close()
    }

    save(): void {
        if (!FormUtils.validateOnSubmit(this.myForm)) return
        this.dialogRef.close(true)
    }

    productSelectionChanged(): void {
        const selectedProduct = this.productSet.find(product => product.id === this.data.command.productId)
        if (selectedProduct && selectedProduct.subProductGroup && selectedProduct.subProductGroup.name) {
            const groupName = selectedProduct.subProductGroup.name
            const subProductGroup = this.data.subProductGroups.find(group => group.name === groupName)
            if (!subProductGroup) return //? report error
            if (subProductGroup?.subProducts.length === 0) {
                this.data.command.subProductAllocations = []
                return
            }
            if (this.data.command.subProductAllocations.length > 0) {
                // Do we need to change?
                const sampleId = (subProductGroup?.subProducts[0].id)
                const test = this.data.command.subProductAllocations.find(allocation =>  sampleId === allocation?.subProduct?.id)
                if (test) {
                    return
                }
            }
            this.data.command.subProductAllocations = []
            for (const subProduct of subProductGroup.subProducts) {
                const subProductAllocation = new SubProductAllocationModel()
                subProductAllocation.subProduct = subProduct
                this.data.command.subProductAllocations.push(subProductAllocation)
            }
        } else {
            this.data.command.subProductAllocations = []
        }
    }

}
