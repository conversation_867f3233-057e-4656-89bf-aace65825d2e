<div class="allocate-product-dialog">

    <form acForm #myForm="ngForm">
        <h3>Opportunity</h3>

        <ac-form-field>
            <label>Opportunity Number:</label>
            <span>{{data.opportunity.opportunityNumber}}</span>
        </ac-form-field>

        <ac-form-field>
            <label>Opportunity Name:</label>
            <span>{{data.opportunity.name}}</span>
        </ac-form-field>

        <ac-form-field>
            <label>Line Item Name:</label>
            <span>{{data.lineItem.name}}</span>
        </ac-form-field>

        <ac-form-field>
            <label>Line Item Description:</label>
            <span>{{data.lineItem.description}}</span>
        </ac-form-field>

        <ac-form-field>
            <label>Salesforce Quantity:</label>
            <span>{{data.lineItem.quantity}}</span>
        </ac-form-field>

        <h3>Product Allocation</h3>

        <ac-form-field [required]="true">
            <label>Company:</label>
            <ng-select
                required
                name="company"
                [(ngModel)]="data.command.deliveredToId"
                [items]="data.companyList"
                [clearable]="false"
                bindLabel="name"
                bindValue="id"
            ></ng-select>
        </ac-form-field>

        <ac-form-field [required]="true">
            <label>Product:</label>
            <ng-select
                required
                name="product"
                [(ngModel)]="data.command.productId"
                (change)="productSelectionChanged()"
            >
                @for (category of data.productList; track category) {
                    <ng-option disabled><div class="select-category">{{category.name}}</div></ng-option>
                    @for (product of category.products; track product) {
                        <ng-option [value]="product.id">{{product.code}} - {{product.title}}</ng-option>
                    }
                }
            </ng-select>
        </ac-form-field>

        <ac-form-field>
            <label>Product Quantity</label>
            <input pInputText
                   type="number"
                   id="input-quantity"
                   name="quantity"
                   validationProperty="quantity"
                   [(ngModel)]="data.command.quantity"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="data.validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

        @if (data.command.subProductAllocations && data.command.subProductAllocations.length > 0) {
            <h3>Sub Products</h3>

            <div class="sub-production-allocations">
                <div class="sub-products-headings-row">
                    <div class="sub-product-identifier">Sub Product</div><div class="sub-product-quantity">Quantity</div>
                </div>
                @for (subAllocation of data.command.subProductAllocations; track subAllocation.subProduct) {
                    <div class="sub-product-identifier">
                    {{subAllocation.subProduct?.code}} - {{subAllocation.subProduct?.title}}
                    </div>
                    <div class="sub-product-quantity">
                        <input pInputText
                               type="number"
                               [id]="subAllocation.subProduct?.code + '-quantity'"
                               [name]="subAllocation.subProduct?.code + '-quantity'"
                               validationProperty="quantity"
                               [(ngModel)]="subAllocation.quantity"
                               [ngModelOptions]="{ updateOn: 'blur' }"
                               acClassFieldValidator [validationClass]="SubProductAllocationDto"
                               [style]="{'width':'100%'}"
                        />
                    </div>
                }
            </div>
        }

        <h3>Contract Information</h3>

        <ac-form-field>
            <label>Contract Term:</label>
            <ng-select
                required
                name="contractTerm"
                [(ngModel)]="data.command.contractTerm"
                [items]="contractTermValues"
                [clearable]="false"
                placeholder="Please Select"
            ></ng-select>
        </ac-form-field>

        <ac-form-field>
            <label>Contract Start Date:</label>
            <p-calendar
                dataType="date"
                dateFormat="dd/mm/yy"
                [(ngModel)]="data.command.contractStartDate"
                name="contractStartDate"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Contract End Date:</label>
            <p-calendar
                dataType="date"
                dateFormat="dd/mm/yy"
                [(ngModel)]="data.command.contractEndDate"
                name="contractEndDate"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Licence Start Date:</label>
            <p-calendar
                dataType="date"
                dateFormat="dd/mm/yy"
                [(ngModel)]="data.command.licenceStartDate"
                name="licenceStartDate"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Licence End Date:</label>
            <p-calendar
                dataType="date"
                dateFormat="dd/mm/yy"
                [(ngModel)]="data.command.licenceEndDate"
                name="licenceEndDate"
            />
        </ac-form-field>

        <ac-button-bar justifyLeft="true">
            <p-button label="Cancel" [rounded]="true" (onClick)="cancel()" />
            <ac-button-spacer></ac-button-spacer>&nbsp;&nbsp;&nbsp;&nbsp;
            <p-button label="Save" [rounded]="true" (onClick)="save()" />
        </ac-button-bar>

    </form>
</div>


