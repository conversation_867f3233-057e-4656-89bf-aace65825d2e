import { Component, Input, OnChanges, Optional, SimpleChanges } from '@angular/core'
import { BillingCurrency, CompanyModel, CompanyListItemModel, CompanyNameModel, MarketSector, Rank, Source } from '@nx-sparta/data-model'
import { ControlContainer, FormsModule, NgForm } from '@angular/forms'
import { InputTextModule } from 'primeng/inputtext'
import { CheckboxModule } from 'primeng/checkbox'
import { NgSelectModule } from '@ng-select/ng-select'
import { DividerModule } from 'primeng/divider'
import { AcClassFieldValidatorDirective, AcFormFieldComponent } from '@nx-sparta/athena-controls'


@Component({
    selector: 'app-company-info',
    standalone: true,
    imports: [
        AcClassFieldValidatorDirective,
        AcFormFieldComponent,
        FormsModule,
        InputTextModule,
        CheckboxModule,
        NgSelectModule,
        DividerModule,
    ],
    templateUrl: './company-info.component.html',
    styleUrl: './company-info.component.scss',
    viewProviders: [
        {
            provide: ControlContainer,
            deps: [[Optional, NgForm]],
            useFactory: (ngForm: NgForm): NgForm => ngForm,
        },
    ],
})
export class CompanyInfoComponent implements OnChanges {

    protected readonly sourceValues = Object.values(Source)
    protected readonly marketSectorValues = Object.values(MarketSector)
    protected readonly rankValues = Object.values(Rank)
    protected readonly billingCurrencyValues = Object.values(BillingCurrency)
    protected readonly Source = Source

    @Input()
    companyDetails?: CompanyModel

    @Input()
    validationClass?: new() => object

    @Input()
    companyList?: CompanyListItemModel[]
    associatableCompanies?: CompanyNameModel[]

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['companyList']) {
            this.allocateAssociatedCompanyList()
        }
    }

    companySourceChanged(_newValue: string): void {
        this.companyDetails!.associatedCompany = undefined
        this.allocateAssociatedCompanyList()
    }

    allocateAssociatedCompanyList(): void {
        // This is re-calculating when source dropdown is changed which could be inefficient, although it's not like
        // people will be choosing from the first dropdown often
        this.associatableCompanies = []
        if (this.companyDetails?.source === Source.Partner) {
            this.associatableCompanies = this.companyList?.filter(value => value.isPartner && value.id !== this.companyDetails?.id)
                .map(x => new CompanyNameModel(x.id, x.name)) as CompanyNameModel[]
        } else if (this.companyDetails?.source === Source.Referrer) {
            this.associatableCompanies = this.companyList?.filter(value => value.isReferrer && value.id !== this.companyDetails?.id)
                .map(x => new CompanyNameModel(x.id, x.name)) as CompanyNameModel[]
        }
    }

}
