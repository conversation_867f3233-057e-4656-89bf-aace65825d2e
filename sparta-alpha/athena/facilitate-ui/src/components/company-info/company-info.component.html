<div class="outer-container">
@if (companyDetails) {
    <section class="section-general">
        <h3>General Information</h3>

        <ac-form-field>
            <label>Name:</label>
            <input pInputText
                   id="input-name"
                   name="name"
                   validationProperty="name"
                   [(ngModel)]="companyDetails.name"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Company Type:</label>
            <p-checkbox [binary]="true" name="isCustomer" [(ngModel)]="companyDetails.isCustomer" label="Customer" />
            &nbsp;
            <p-checkbox [binary]="true" name="isPartner" [(ngModel)]="companyDetails.isPartner" label="Partner" />
            &nbsp;
            <p-checkbox [binary]="true" name="isReseller" [(ngModel)]="companyDetails.isReferrer" label="Referrer" />
        </ac-form-field>

        <ac-form-field>
            <label>Market Sector:</label>
            <ng-select
                name="sector"
                [(ngModel)]="companyDetails.marketSector"
                [items]="marketSectorValues"
                [clearable]="false"
            ></ng-select>
        </ac-form-field>

        <p-divider />

        <ac-form-field>
            <label>Customer Success Manager:</label>
            <input pInputText
                   id="input-customer-success-manager"
                   name="customer-success-manager"
                   validationProperty="customerSuccessManager"
                   [(ngModel)]="companyDetails.customerSuccessManager"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Commercial Account Manager:</label>
            <input pInputText
                   id="input-commercial-account-manager"
                   name="commercial-account-manager"
                   validationProperty="commercialAccountManager"
                   [(ngModel)]="companyDetails.commercialAccountManager"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

    </section>

    <section class="section-salesforce">
        <h3>Salesforce Information</h3>

        @if (!companyDetails.salesforceAccount) {
            <p>This company is not linked to a Salesforce Account.</p>
        } @else {
            <ac-form-field>
                <label>Account Name:</label>
                <span>{{companyDetails.salesforceAccount.name}}</span>
            </ac-form-field>

            <ac-form-field>
                <label>Address:</label>
                <span><pre>{{companyDetails.salesforceAccount.address}}</pre></span>
            </ac-form-field>

            <ac-form-field>
                <label>Phone:</label>
                <span>{{companyDetails.salesforceAccount.phone}}</span>
            </ac-form-field>

            <ac-form-field>
                <label>Website:</label>
                <span>{{companyDetails.salesforceAccount.website}}</span>
            </ac-form-field>

            <ac-form-field>
                <label>Company Number:</label>
                <span>{{companyDetails.salesforceAccount.companyNumber}}</span>
            </ac-form-field>
        }
    </section>

    <section class="section-financial">
        <h3>Financial / Legal Information</h3>

        <ac-form-field [required]="true">
            <label>Company Source:</label>
            <ng-select
                required
                name="source"
                [(ngModel)]="companyDetails.source"
                [items]="sourceValues"
                [clearable]="false"
                placeholder="Please Select"
                (change)="companySourceChanged($event)"
            ></ng-select>
        </ac-form-field>

        @if (companyDetails.source === Source.Partner || companyDetails.source === Source.Referrer) {
            <ac-form-field>
                <label>Associated {{companyDetails.source}}:</label>
                <ng-select
                    name="associatedCompany"
                    [(ngModel)]="companyDetails.associatedCompanyId"
                    [items]="associatableCompanies"
                    [clearable]="true"
                    [bindLabel]="'name'"
                    [bindValue]="'id'"
                    placeholder="Please Select"
                ></ng-select>
            </ac-form-field>
        }

        <ac-form-field [required]="true">
            <label>Company Rank:</label>
            <ng-select
                required
                name="rank"
                [(ngModel)]="companyDetails.rank"
                [items]="rankValues"
                [clearable]="false"
                placeholder="Please Select"
            ></ng-select>
        </ac-form-field>

        <ac-form-field>
            <label>Master Services Agreement:</label>
            <input pInputText
                   id="input-masterServicesAgreement"
                   name="masterServicesAgreement"
                   validationProperty="masterServicesAgreement"
                   [(ngModel)]="companyDetails.masterServicesAgreement"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

        <ac-form-field>
            <label>Financial Due Diligence Report:</label>
            <input pInputText
                   id="input-financialDueDiligenceReport"
                   name="financialDueDiligenceReport"
                   validationProperty="financialDueDiligenceReport"
                   [(ngModel)]="companyDetails.financialDueDiligenceReport"
                   [ngModelOptions]="{ updateOn: 'blur' }"
                   acClassFieldValidator [validationClass]="validationClass"
                   [style]="{'width':'100%'}"
            />
        </ac-form-field>

        <ac-form-field [required]="true">
            <label>Billing Currency:</label>
            <ng-select
                required
                name="currency"
                [(ngModel)]="companyDetails.billingCurrency"
                [items]="billingCurrencyValues"
                [clearable]="false"
                placeholder="Please Select"
            ></ng-select>
        </ac-form-field>

    </section>
}
</div>
