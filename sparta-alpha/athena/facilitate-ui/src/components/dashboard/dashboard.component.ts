import { Component, OnInit } from '@angular/core'
import { DatePipe } from '@angular/common'
import { PrimeTemplate } from 'primeng/api'
import { TableModule } from 'primeng/table'
import { AcContentWrapperComponent, AcHeaderBarComponent, SpinnerService, TypedHttpClientService } from '@nx-sparta/athena-controls'
import { DashboardDataDto } from '@nx-sparta/data-model'

@Component({
    selector: 'app-dashboard',
    standalone: true,
    imports: [
        AcContentWrapperComponent,
        AcHeaderBarComponent,
        DatePipe,
        PrimeTemplate,
        TableModule,
    ],
    templateUrl: './dashboard.component.html',
    styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {

    dashboardData?: DashboardDataDto

    constructor(
        private typedClient: TypedHttpClientService,
        private spinnerService: SpinnerService,
    ) {}

    ngOnInit(): void {
        this.fetchData()
    }

    fetchData(): void {
        this.spinnerService.showSpinner()
        this.typedClient.getTyped<DashboardDataDto>(DashboardDataDto, `/facilitate-api/dashboard`).subscribe(
            data => {
                this.spinnerService.hideSpinner()
                this.dashboardData = data
            },
        )
    }

}
