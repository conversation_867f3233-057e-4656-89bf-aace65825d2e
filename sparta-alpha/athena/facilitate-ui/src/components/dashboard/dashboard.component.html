<ac-header-bar>Dashboard</ac-header-bar>
<ac-content-wrapper>
    @if (dashboardData) {

        <div class="stats-row">
            <div class="stats-item">
                <div class="stats-label">Companies</div>
                <div class="stats-value">{{dashboardData.companyCount}}</div>
            </div>
            <div class="stats-item">
                <div class="stats-label">Opportunities</div>
                <div class="stats-value">{{dashboardData.opportunityCount}}</div>
            </div>
        </div>

        <div class="section-expiries">
            <h3>Upcoming Contract Expiry</h3>
            @if (dashboardData.upcomingContractExpiry.length === 0) {
                <div>None</div>
            } @else {

                <p-table
                    [value]="dashboardData.upcomingContractExpiry"
                    [tableStyle]="{ 'min-width': '50rem' }"
                    styleClass="p-datatable-striped"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="companyName">Company<p-sortIcon field="companyName" /></th>
                            <th pSortableColumn="code">Code<p-sortIcon field="code" /></th>
                            <th pSortableColumn="title">Title<p-sortIcon field="title" /></th>
                            <th pSortableColumn="contractEndDate">Contract End<p-sortIcon field="contractEndDate" /></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-productExpiry>
                        <tr>
                            <td>{{ productExpiry.companyName }}</td>
                            <td>{{ productExpiry.code }}</td>
                            <td>{{ productExpiry.title }}</td>
                            <td>{{ productExpiry.contractEndDate }}</td>
                        </tr>
                    </ng-template>
                </p-table>
            }
        </div>

        <div class="section-expiries">
            <h3>Upcoming Licence Expiry</h3>
            @if (dashboardData.upcomingLicenceExpiry.length === 0) {
                <div>None</div>
            } @else {

                <p-table
                    [value]="dashboardData.upcomingLicenceExpiry"
                    [tableStyle]="{ 'min-width': '50rem' }"
                    styleClass="p-datatable-striped"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="companyName">Company<p-sortIcon field="companyName" /></th>
                            <th pSortableColumn="code">Code<p-sortIcon field="code" /></th>
                            <th pSortableColumn="title">Title<p-sortIcon field="title" /></th>
                            <th pSortableColumn="licenceEndDate">Licence End<p-sortIcon field="licenceEndDate" /></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-productExpiry>
                        <tr>
                            <td>{{ productExpiry.companyName }}</td>
                            <td>{{ productExpiry.code }}</td>
                            <td>{{ productExpiry.title }}</td>
                            <td>{{ productExpiry.licenceEndDate }}</td>
                        </tr>
                    </ng-template>
                </p-table>

            }

        </div>

        <div class="section-expiries">
            <h3>Recent Contract Expiry</h3>
            @if (dashboardData.recentContractExpiry.length === 0) {
                <div>None</div>
            } @else {

                <p-table
                    [value]="dashboardData.recentContractExpiry"
                    [tableStyle]="{ 'min-width': '50rem' }"
                    styleClass="p-datatable-striped"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="companyName">Company<p-sortIcon field="companyName" /></th>
                            <th pSortableColumn="code">Code<p-sortIcon field="code" /></th>
                            <th pSortableColumn="title">Title<p-sortIcon field="title" /></th>
                            <th pSortableColumn="contractEndDate">Contract End<p-sortIcon field="contractEndDate" /></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-productExpiry>
                        <tr>
                            <td>{{ productExpiry.companyName }}</td>
                            <td>{{ productExpiry.code }}</td>
                            <td>{{ productExpiry.title }}</td>
                            <td>{{ productExpiry.contractEndDate }}</td>
                        </tr>
                    </ng-template>
                </p-table>

            }

        </div>

        <div class="section-expiries">
            <h3>Recent Licence Expiry</h3>
            @if (dashboardData.recentLicenceExpiry.length === 0) {
                <div>None</div>
            } @else {

                <p-table
                    [value]="dashboardData.recentLicenceExpiry"
                    [tableStyle]="{ 'min-width': '50rem' }"
                    styleClass="p-datatable-striped"
                >
                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="companyName">Company<p-sortIcon field="companyName" /></th>
                            <th pSortableColumn="code">Code<p-sortIcon field="code" /></th>
                            <th pSortableColumn="title">Title<p-sortIcon field="title" /></th>
                            <th pSortableColumn="licenceEndDate">Licence End<p-sortIcon field="licenceEndDate" /></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-productExpiry>
                        <tr>
                            <td>{{ productExpiry.companyName }}</td>
                            <td>{{ productExpiry.code }}</td>
                            <td>{{ productExpiry.title }}</td>
                            <td>{{ productExpiry.licenceEndDate }}</td>
                        </tr>
                    </ng-template>
                </p-table>

            }

        </div>

    }
</ac-content-wrapper>
