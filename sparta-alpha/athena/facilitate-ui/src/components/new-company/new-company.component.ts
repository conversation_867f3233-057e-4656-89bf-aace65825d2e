import { Component, OnInit, ViewChild } from '@angular/core'
import { CompanyModel, CompanyListItemModel, CreateCompanyCommand } from '@nx-sparta/data-model'
import { Router } from '@angular/router'
import { FormsModule, NgForm } from '@angular/forms'
import { CompanyInfoComponent } from '../company-info/company-info.component'
import { Button } from 'primeng/button'
import {
    AcContentWrapperComponent,
    AcFormDirective,
    AcHeaderBarComponent, AcLogger,
    ButtonBarComponent,
    ClientCommandMapperUtil,
    FormUtils,
    SpinnerService,
    TypedHttpClientService,
} from '@nx-sparta/athena-controls'


@Component({
    selector: 'app-new-company',
    standalone: true,
    imports: [
        FormsModule,
        AcHeaderBarComponent,
        CompanyInfoComponent,
        Button,
        ButtonBarComponent,
        AcContentWrapperComponent,
        AcFormDirective,
    ],
    templateUrl: './new-company.component.html',
    styleUrl: './new-company.component.scss',
})
export class NewCompanyComponent implements OnInit {

    companyDetails: CompanyModel = new CompanyModel()
    protected readonly CreateCompanyCommand = CreateCompanyCommand

    @ViewChild('myForm') myForm?: NgForm

    companyList?: CompanyListItemModel[]

    constructor(
        private typedClient: TypedHttpClientService,
        private router: Router,
        private spinnerService: SpinnerService,
    ) {}

    ngOnInit(): void {
        this.typedClient.getTyped<CompanyListItemModel[]>(CompanyListItemModel, '/facilitate-api/companies').subscribe(
            data => {
                this.companyList = data
            },
        )
    }

    cancel(): void {
        this.router.navigateByUrl('company-list')
    }

    save(): void {

        if (!this.companyDetails) return
        if (!FormUtils.validateOnSubmit(this.myForm)) return
        const command =
            ClientCommandMapperUtil.mapObjectFromTo<CompanyModel, CreateCompanyCommand>(this.companyDetails, CreateCompanyCommand)

        this.spinnerService.showSpinner()
        this.typedClient.postTyped<CompanyModel>(CompanyModel, `facilitate-api/companies`, command).subscribe(
            _result => {
                this.spinnerService.hideSpinner()
                this.router.navigateByUrl('company-list')
            },
            error => {
                this.spinnerService.hideSpinner()
                AcLogger.log(error)
                alert(error.message)
            },
        )
    }

}
