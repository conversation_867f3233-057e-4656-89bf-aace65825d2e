import { AfterViewInit, Component, ViewChild } from '@angular/core'
import { FormsModule, NgForm } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { CompanyContactComponent } from '../company-contact/company-contact.component'
import { But<PERSON> } from 'primeng/button'
import { InputTextModule } from 'primeng/inputtext'
import { TableModule } from 'primeng/table'
import { AllocateProductDialog } from '../allocate-product-dialog/allocate-product.dialog'
import { CheckboxModule } from 'primeng/checkbox'
import { NgSelectModule } from '@ng-select/ng-select'
import { DividerModule } from 'primeng/divider'
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog'
import { DatePipe, NgClass } from '@angular/common'
import { CompanyInfoComponent } from '../company-info/company-info.component'
import {
    AcClassFieldValidatorDirective,
    AcContentWrapperComponent,
    AcFormDirective,
    AcFormFieldComponent,
    AcHeaderBarComponent, AcLogger,
    ButtonBarComponent,
    ButtonSpacerComponent, ClientCommandMapperUtil, FormService, FormUtils, SpinnerService, TypedHttpClientService,
} from '@nx-sparta/athena-controls'
import {
    BillingCurrency,
    CompanyModel,
    CompanyListItemModel, ContractTerm, CreateProductAllocationCommand,
    MarketSector, ProductAllocationCommand, ProductAllocationViewModel,
    ProductCategoryModel,
    Rank, SalesforceLineItemModel,
    SalesforceOpportunityModel,
    Source,
    SubProductGroupModel,
    UpdateCompanyCommand, UpdateProductAllocationCommand,
} from '@nx-sparta/data-model'

@Component({
    selector: 'app-company-details',
    standalone: true,
    imports: [
        AcClassFieldValidatorDirective,
        AcContentWrapperComponent,
        AcFormDirective,
        AcFormFieldComponent,
        AcHeaderBarComponent,
        Button,
        ButtonBarComponent,
        ButtonSpacerComponent,
        CompanyContactComponent,
        CheckboxModule,
        FormsModule,
        InputTextModule,
        TableModule,
        NgSelectModule,
        DividerModule,
        DynamicDialogModule,
        DatePipe,
        NgClass,
        CompanyInfoComponent,
    ],
    providers: [ DialogService ],
    templateUrl: './company-details.component.html',
    styleUrl: './company-details.component.scss',
})
export class CompanyDetailsComponent implements AfterViewInit {

    protected readonly sourceValues = Object.values(Source)
    protected readonly marketSectorValues = Object.values(MarketSector)
    protected readonly rankValues = Object.values(Rank)
    protected readonly billingCurrencyValues = Object.values(BillingCurrency)
    protected readonly UpdateCompanyCommand = UpdateCompanyCommand
    protected readonly Source = Source

    selectedTabIndex: number = 0

    @ViewChild('myForm') myForm?: NgForm

    companyId!: string | null
    companyDetails?: CompanyModel

    companyList?: CompanyListItemModel[]
    productList?: ProductCategoryModel[]
    subProductGroups?: SubProductGroupModel[]

    constructor(
        private typedClient: TypedHttpClientService,
        private router: Router,
        private route: ActivatedRoute,
        private dialogService: DialogService,
        private spinnerService: SpinnerService,
        private formService: FormService,
    ) {}

    onTabChange(tabNumber: number): void {
        if (this.selectedTabIndex === 0 && this.myForm?.dirty) {
            this.formService.confirmClose(this.myForm).then(
                _res => {
                    this.cancel()
                    this.selectedTabIndex = tabNumber
                }, _rej => {

                },
            )
        } else {
            this.selectedTabIndex = tabNumber
        }
    }

    ngAfterViewInit(): void {
        this.companyId = this.route.snapshot.paramMap.get('companyId')
        this.fetchDetails(this.companyId)

        this.typedClient.getTyped<CompanyListItemModel[]>(CompanyListItemModel, '/facilitate-api/companies').subscribe(
            data => {
                this.companyList = data
            },
        )
        this.typedClient.getTyped<ProductCategoryModel[]>(ProductCategoryModel, '/facilitate-api/products').subscribe(
            data => {
                this.productList = data
            },
        )
        this.typedClient.getTyped<SubProductGroupModel[]>(SubProductGroupModel, '/facilitate-api/sub-product-groups').subscribe(
            data => {
                this.subProductGroups = data
            },
        )
    }

    fetchDetails(companyId: string | null): void {
        if (companyId == null) return

        this.spinnerService.showSpinner()
        this.typedClient.getTyped<CompanyModel>(CompanyModel, `/facilitate-api/company/${companyId}`).subscribe(
            data => {
                this.spinnerService.hideSpinner()
                this.companyDetails = data
                // When fetch is called due pressing cancel we need to reset the form.
                // Have to check status of myform the first time as form not created until after first fetch as
                // html not made until after companyDetails are populated the first time.
                if (this.myForm) FormUtils.markAllAsPristineUntouched(this.myForm)
            },
        )
    }

    save(): void {
        if (!this.companyDetails || !this.companyDetails.id) return
        if (!FormUtils.validateOnSubmit(this.myForm)) return
        const command =
            ClientCommandMapperUtil.mapObjectFromTo<CompanyModel, UpdateCompanyCommand>(this.companyDetails, UpdateCompanyCommand)

        this.spinnerService.showSpinner()
        this.typedClient.postTyped<CompanyModel>(CompanyModel, `facilitate-api/company/${this.companyDetails.id}`, command).subscribe(
            _result => {
                this.spinnerService.hideSpinner()
            },
            error => {
                this.spinnerService.hideSpinner()
                AcLogger.log(error)
                alert(error.message)
            },
        )
    }

    backToList(): void {
        this.router.navigateByUrl('company-list')
    }

    cancel(): void {
        this.fetchDetails(this.companyId)
    }

    hasOpportunities(): boolean {
        const val = this.companyDetails?.salesforceAccount?.salesforceOpportunities?.length
        if (val === null || val === undefined) return false
        return (val > 0)
    }

    hasProductAllocations(): boolean {
        const val = this.companyDetails?.productAllocations?.length
        if (val === null || val === undefined) return false
        return (val > 0)
    }

    opportunities(): SalesforceOpportunityModel[] {
        const retval = this.companyDetails?.salesforceAccount?.salesforceOpportunities
        if (retval === null || retval === undefined) return []
        return retval
    }

    allocateLineItem(opportunity: SalesforceOpportunityModel, lineItem: SalesforceLineItemModel): void {
        const command = new CreateProductAllocationCommand()
        command.salesforceLineItemId = lineItem.id
        command.quantity = lineItem.quantity
        command.contractTerm = ContractTerm.OneOff
        if (! (this.companyDetails!.isPartner || this.companyDetails!.isReferrer)) {
            // If the current company is marked as a partner or referrer leave the company drop down as a choice
            // the user must make.
            // Otherwise, even if not actually selected they are considered a customer themselves and we can
            // default to the deliver-to allocation being for themselves.
            command.deliveredToId = this.companyDetails!.id
        }
        this.openProductAllocationDialogFor(opportunity, lineItem, command, CreateProductAllocationCommand, '/facilitate-api/allocate-product')
    }

    editAllocation(opportunity: SalesforceOpportunityModel, lineItem: SalesforceLineItemModel): void {
        if (!lineItem.productAllocation) throw new Error('Can not edit a non existing allocation')
        if (lineItem.productAllocation.salesforceLineItem.id != lineItem.id) throw new Error('Inconsistent Data')

        const command =
            ClientCommandMapperUtil.mapObjectFromTo<ProductAllocationViewModel, UpdateProductAllocationCommand>(lineItem.productAllocation, UpdateProductAllocationCommand)
        command.productId = lineItem.productAllocation.product.id
        command.deliveredToId = lineItem.productAllocation.deliveredTo.id
        command.salesforceLineItemId = lineItem.productAllocation.salesforceLineItem.id

        this.openProductAllocationDialogFor(opportunity, lineItem, command, UpdateProductAllocationCommand, `/facilitate-api/product-allocation/${lineItem.productAllocation.id}`)
    }

    openProductAllocationDialogFor(
        opportunity: SalesforceOpportunityModel,
        lineItem: SalesforceLineItemModel,
        productAllocationCommand: ProductAllocationCommand,
        validationClass: new() => object,
        postUrl: string,
    ): void {
        const dialogRef = this.dialogService.open(AllocateProductDialog, {
            header: 'Product Allocation',
            data: {
                opportunity,
                lineItem,
                companyList: this.companyList,
                productList: this.productList,
                subProductGroups: this.subProductGroups,
                command: productAllocationCommand,
                validationClass,
            },
            width: '900px',
        })

        dialogRef.onClose.subscribe(result => {
            if (result) {
                this.spinnerService.showSpinner()
                this.typedClient.postTyped<ProductAllocationViewModel>(ProductAllocationViewModel, postUrl, productAllocationCommand).subscribe(
                    _allocResult => {
                        this.spinnerService.hideSpinner()
                        // TODO This could be more optimal, we are calling allocate but then making another call to get changes to entire company
                        // This is in part because the object created/edited could exist as an item under a salesforce line item or in the list of product allocations
                        // for the current company or both. Whilst a client side calculation of patching the updated data into place is possible it is quick and easy
                        // to just re-fetch.
                        this.fetchDetails(this.companyId)
                    },
                    error => {
                        this.spinnerService.hideSpinner()
                        AcLogger.log(error)
                        alert(error.message)
                    },
                )
            }
        })
    }



}
