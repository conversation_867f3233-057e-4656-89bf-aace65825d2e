<ac-header-bar>Company: {{companyDetails?.name}}</ac-header-bar>
<ac-content-wrapper>
@if (companyDetails) {
    <div class="top-link-row">
        <div class="back-link" (click)="backToList()"><i class="pi pi-arrow-left"></i> Back to Company List</div>
    </div>

    <div class="top-nav-panel">
        <div class="tab" (click)="onTabChange(0)" [ngClass]="{ 'selected': selectedTabIndex === 0}">Information</div>
        @if (companyDetails.salesforceAccount) {
            <div class="tab" (click)="onTabChange(1)" [ngClass]="{ 'selected': selectedTabIndex === 1}">Opportunities</div>
        }
        <div class="tab" (click)="onTabChange(2)" [ngClass]="{ 'selected': selectedTabIndex === 2}">Products</div>
    </div>
    <div class="company-details">
        @if (selectedTabIndex === 0) {
            <form acForm #myForm="ngForm">

                <app-company-info [companyDetails]="companyDetails"
                                  [validationClass]="UpdateCompanyCommand"
                                  [companyList]="companyList"
                ></app-company-info>

                <ac-button-bar>
                    <p-button label="Save" [rounded]="true" (onClick)="save()" />
                </ac-button-bar>
            </form>
        } @else if (selectedTabIndex === 1) {

                <section class="section-opportunities">
                        <h3>Opportunities</h3>
                            <div class="line-item-row">
                                <div class="line-item-name">Name</div>
                                <div class="line-item-description">Description</div>
                                <div class="line-item-allocation">Allocation</div>
                            </div>
                        @for (opportunity of opportunities(); track opportunity.id) {
                            <div class="opportunity-row">
                                <h4>
                                    {{opportunity.opportunityNumber}}:&nbsp;&nbsp;{{opportunity.name}}
                                </h4>
                                <span class="closed-info">(Closed: {{opportunity.closeDate | date : 'dd/MM/y'}})</span>
                                <div class="opportunity-data-row">
                                @if (opportunity.lineItems.length === 0) {
                                    <div class="no-items">No Line Items!</div>
                                } @else {
                                    @for (lineItem of opportunity.lineItems; track lineItem.id) {
                                        <div class="line-item-row">
                                            <div class="line-item-name">{{ lineItem.name }}</div>
                                            <div class="line-item-description">{{ lineItem.description }}</div>
                                            <div class="line-item-allocation">
                                                @if (lineItem.productAllocation) {
                                                    <div class="edit-link" (click)="editAllocation(opportunity, lineItem)"><i class="pi pi-pen-to-square"></i> {{lineItem.productAllocation.deliveredTo.name}}</div>
<!--                                                    <div>{{lineItem.productAllocation.deliveredTo.name}}</div>-->
<!--                                                    <p-button class="line-button" label="E" (onClick)="editAllocation(opportunity, lineItem)" />-->
                                                } @else {
                                                    <p-button class="line-button" label="Allocate" (onClick)="allocateLineItem(opportunity, lineItem)" />
                                                }
                                            </div>
                                        </div>
                                    }
                                }
                                </div>
                            </div>
                        }
                    </section>

        } @else if (selectedTabIndex === 2) {

            <section class="section-allocations">
                <h3>Product Allocation</h3>
                @if (hasProductAllocations()) {

                    <p-table
                        [value]="companyDetails.productAllocations"
                        [tableStyle]="{ 'min-width': '50rem' }"
                        styleClass="p-datatable-striped"
                        [paginator]="true"
                        [rows]="15"
                    >
                        <ng-template pTemplate="header">
                            <tr>
                                <th pSortableColumn="code">Code<p-sortIcon field="product.code" /></th>
                                <th pSortableColumn="title">Title<p-sortIcon field="title" /></th>
                                <th pSortableColumn="quantity">Quantity<p-sortIcon field="quantity" /></th>
                                <th pSortableColumn="contractStartDate">Contract Start<p-sortIcon field="contractStartDate" /></th>
                                <th pSortableColumn="contractEndDate">Contract End<p-sortIcon field="contractEndDate" /></th>
                                <th pSortableColumn="licenceStartDate">Licence Start<p-sortIcon field="licenceStartDate" /></th>
                                <th pSortableColumn="licenceEndDate">Licence End<p-sortIcon field="licenceEndDate" /></th>
                                <th pSortableColumn="ownedBy">Owned By<p-sortIcon field="ownedBy" /></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-productAllocation>
                            <tr>
                                <td>{{ productAllocation.product.code }}</td>
                                <td>{{ productAllocation.product.title }}</td>
                                <td>{{ productAllocation.quantity }}</td>
                                <td>{{ productAllocation.contractStartDate }}</td>
                                <td>{{ productAllocation.contractEndDate }}</td>
                                <td>{{ productAllocation.licenceStartDate }}</td>
                                <td>{{ productAllocation.licenceEndDate }}</td>
                                <td>{{ productAllocation.ownedBy.name }}</td>
                            </tr>
                        </ng-template>
                    </p-table>


                } @else {
                    <h5>No Products found</h5>
                }
            </section>

        }

    </div>
}
</ac-content-wrapper>
