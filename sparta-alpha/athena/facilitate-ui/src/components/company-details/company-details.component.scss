@import "includes/common";

.top-link-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
}

.top-nav-panel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    background-color: #fdfdfd;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 15px;
}

.tab {
    min-width: 50px;
    padding: 20px;
    font-family: "Inter var", sans-serif;
    font-weight: 700;
    color: #6b7280;

    &.selected {
        color: #209DD8;
        border-bottom: 2px solid #209DD8;
        margin-bottom: -2px;
    }

    &:not(.selected):hover {
        border-bottom: 2px solid grey;
        margin-bottom: -2px;
    }
}

.back-link {
    margin-bottom: 15px;
    cursor: pointer;

    i {
        font-size: 12px;
    }

    &:hover {
        color: #209DD8;
    }
}

.edit-link {

    padding: 5px 2px;

    cursor: pointer;

    i {
        font-size: 12px;
    }

    &:hover {
        color: #209DD8;
    }


}

.company-details {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;

    form {
        display: contents;
    }
}

mat-divider {
    margin-bottom: 10px;
}

section {
    @include content-panel();
    padding: 5px 12px;
    width: 49%;
    min-width: 650px;
    flex: 1 0 auto;

    h3 {
        font-size: 20px;
        margin: 6px 0px 18px;
    }
}

.section-opportunities, .section-allocations {
    min-width: 75%;
}

.section-opportunities {
    h3 {
        margin-bottom: 5px;
    }
}

.opportunity-row {
    h4 {
        padding-left: 5px;
        margin: 10px 0px 0px;
        font-size: 17px;
    }
    margin-bottom: 20px;
}

.closed-info {
    font-size: 13px;
    padding-left: 5px;
    font-style: normal;
}


.opportunity-data-row {
    padding-left: 5px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.no-items {
    padding: 5px;
    border-bottom: 1px solid #dedede;
    width: 100%;
}

.line-item-row {
    flex-grow: 1;
    width: 100%;
    min-width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #dedede;
    gap: 22px;
    overflow: hidden;
}

.line-item-name {
    flex: 0 0 auto;
    width: 42%;
}

.line-item-description {
    flex-grow: 1;
}

.line-item-allocation {
    width: 200px;
    min-width: 200px;
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
}
