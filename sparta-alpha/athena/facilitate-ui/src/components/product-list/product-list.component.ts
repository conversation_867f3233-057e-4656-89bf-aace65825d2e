import { Component, OnInit } from '@angular/core'
import { PrimeTemplate } from 'primeng/api'
import { Router } from '@angular/router'
import { Button } from 'primeng/button'
import { AcContentWrapperComponent, AcHeaderBarComponent, ButtonBarComponent, TypedHttpClientService } from '@nx-sparta/athena-controls'
import { ProductCategoryModel } from '@nx-sparta/data-model'

@Component({
    selector: 'app-product-list',
    standalone: true,
    imports: [
        AcContentWrapperComponent,
        AcHeaderBarComponent,
        PrimeTemplate,
        Button,
        ButtonBarComponent,
    ],
    templateUrl: './product-list.component.html',
    styleUrl: './product-list.component.scss',
})
export class ProductListComponent implements OnInit {

    productList: ProductCategoryModel[] = []

    constructor(
        private typedClient: TypedHttpClientService,
        private router: Router,
    ) {
    }

    ngOnInit(): void {
        this.typedClient.getTyped<ProductCategoryModel[]>(ProductCategoryModel, '/facilitate-api/products').subscribe(
            (data: ProductCategoryModel[]) => {
                this.productList = data
            },
        )
    }

    createNew(): void {

    }

}
