<ac-header-bar>Products</ac-header-bar>
<ac-content-wrapper>
    <ac-button-bar justifyLeft="true">
        <p-button icon="pi pi-plus" disabled label="Create" [rounded]="true" (onClick)="createNew()" />
    </ac-button-bar>
    <div class="product-list">

        <table class="product-table">
            <tr class="product-table-header">
                <th>Description</th>
                <th>Code</th>
                <th>Title</th>
                <th>Sub Products</th>
            </tr>

            @for (category of productList; track category) {
                <tr class="product-table-category"><td colspan="4">{{category.name}}</td></tr>
                @for (product of category.products; track product) {
                    <tr class="product-table-product">
                        <td>{{product.description}}</td>
                        <td>{{product.code}}</td>
                        <td>{{product.title}}</td>
                        <td>{{ product.subProductGroup !== null }}</td>
                    </tr>
                }
            }

        </table>

    </div>
</ac-content-wrapper>
