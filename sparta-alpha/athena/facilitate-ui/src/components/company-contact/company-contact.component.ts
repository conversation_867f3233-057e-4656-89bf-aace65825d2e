import { Component, Input } from '@angular/core'
import { AcForm<PERSON>ieldComponent } from '@nx-sparta/athena-controls'
import { CompanyContactModel } from '@nx-sparta/data-model'

@Component({
    selector: 'app-company-contact',
    standalone: true,
    imports: [
        AcFormFieldComponent,
    ],
    templateUrl: './company-contact.component.html',
    styleUrl: './company-contact.component.scss',
})
export class CompanyContactComponent {

    @Input() companyContact?: CompanyContactModel | null

}
