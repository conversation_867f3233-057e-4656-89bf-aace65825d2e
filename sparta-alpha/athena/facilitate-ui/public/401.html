<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <title>Facilitate - Authorization Required</title>
        <style>

            body {
                font-family: Helvetica, sans-serif;
                font-size: 10px;
                font-weight: 200;
                line-height: 1.5;
                background: white;
                color: black;
                padding: 0;
                margin: 0;
            }

            .container {
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                box-sizing: border-box;
                padding: 30px;
            }

            .content {
                position: relative;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                font-size:2em;
                box-sizing: border-box;
            }

            h1 {
                font-size: 26px;
            }

            a {
                color: black;
            }

        </style>
    </head>
    <body>
        <div class="container">
            <div class="content">
                <svg width="600px" xmlns="http://www.w3.org/2000/svg" viewBox="0 310 1200 600"><defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2 {
        stroke-width: 0px;
      }

      .cls-3 {
        letter-spacing: -.1em;
      }

      .cls-4 {
          fill: #000;
          font-size: 120px;
      }

      .cls-4, .cls-5 {
          font-family: Tahoma, Tahoma;
      }

      .cls-2 {
        fill: none;
      }

      .cls-6 {
        letter-spacing: -.07em;
      }

      .cls-7 {
        clip-path: url(#clippath);
      }

      .cls-5 {
        fill: #000;
        font-size: 120px;
      }
    </style>
    <clipPath id="clippath">
      <path class="cls-2" d="M780.39,388.83c-2.31-2.47-5.51-4-9.04-4s-6.75,1.51-9.04,3.96l-.04.04c-2.29,2.45-3.71,5.87-3.71,9.67s1.43,7.19,3.74,9.67v.04c2.29,2.45,5.49,3.96,9.04,3.96s6.73-1.53,9.04-4c2.31-2.47,3.74-5.89,3.74-9.67s-1.42-7.22-3.71-9.67h-.04ZM881.93,497.4c-2.31-2.47-5.51-4-9.04-4s-6.75,1.52-9.04,3.96l-.04.04c-2.29,2.45-3.71,5.87-3.71,9.67s1.43,7.19,3.74,9.67v.04c2.29,2.45,5.49,3.96,9.04,3.96s6.73-1.53,9.04-4c2.31-2.47,3.74-5.89,3.74-9.67s-1.42-7.22-3.71-9.67h-.04ZM775.13,503.68c-2.31-2.47-5.51-4-9.04-4s-6.75,1.52-9.04,3.96l-.04.04c-2.29,2.45-3.71,5.87-3.71,9.67s1.43,7.19,3.74,9.67v.04c2.29,2.45,5.49,3.96,9.04,3.96s6.73-1.53,9.04-4c2.31-2.47,3.74-5.89,3.74-9.67s-1.42-7.22-3.71-9.67h-.04ZM340.14,648.38c-2.31-2.47-5.51-4-9.04-4s-6.75,1.52-9.04,3.96l-.04.04c-2.29,2.45-3.71,5.87-3.71,9.67s1.43,7.19,3.74,9.67v.04c2.29,2.45,5.49,3.96,9.04,3.96s6.73-1.53,9.04-4c2.31-2.47,3.74-5.89,3.74-9.67s-1.42-7.22-3.71-9.67h-.04ZM771.35,364.75c8.72,0,16.61,3.78,22.32,9.89l-.02.02c5.72,6.13,9.26,14.56,9.26,23.84s-3.53,17.76-9.25,23.86c-5.71,6.11-13.6,9.89-22.32,9.89-4.1,0-8.03-.85-11.63-2.38l-99.81,106.71-.03-.03c-7.7,8.22-16.86,14.85-27.02,19.39-9.77,4.36-20.45,6.81-31.61,6.9l9.95,10.64c5.06,5.04,10.96,9.15,17.47,12.06,6.95,3.1,14.61,4.82,22.69,4.82s15.74-1.71,22.69-4.82c7.25-3.24,13.75-7.97,19.17-13.82h0s43.47-46.03,43.47-46.03c-1.4-3.82-2.18-7.98-2.18-12.33,0-9.27,3.53-17.7,9.25-23.82l.02-.02.02-.02c5.73-6.11,13.61-9.88,22.28-9.88s16.61,3.78,22.32,9.88l-.02.02c5.72,6.13,9.26,14.56,9.26,23.84s-3.53,17.76-9.25,23.86c-5.71,6.11-13.61,9.89-22.32,9.89-4.17,0-8.16-.87-11.81-2.46l-43.62,46.19-.02.02h0c-7.69,8.21-16.84,14.83-26.99,19.36-9.77,4.36-20.45,6.81-31.61,6.9l8.5,9.08-.02.03c5.37,5.74,11.79,10.39,18.95,13.59,6.95,3.1,14.61,4.82,22.69,4.82s15.74-1.71,22.69-4.82c7.15-3.2,13.58-7.84,18.95-13.59l-.02-.02,99.79-106.7c-1.43-3.85-2.22-8.05-2.22-12.45,0-9.27,3.53-17.7,9.25-23.82l.02-.02.02-.02c5.73-6.11,13.61-9.89,22.28-9.89s16.61,3.78,22.32,9.89l-.02.02c5.72,6.13,9.26,14.56,9.26,23.84s-3.53,17.76-9.25,23.86c-5.71,6.11-13.6,9.89-22.32,9.89-4.1,0-8.03-.85-11.63-2.38l-99.8,106.71-.02-.03c-7.7,8.22-16.86,14.85-27.02,19.39-9.97,4.45-20.87,6.91-32.27,6.91s-22.31-2.46-32.27-6.91c-10.16-4.54-19.32-11.17-27.02-19.39l-.03.03-99.07-105.92.07-.08-.08-.08-.05.05c-5.41-5.85-11.91-10.58-19.17-13.82-6.95-3.1-14.61-4.82-22.69-4.82s-15.74,1.71-22.69,4.82c-7.15,3.2-13.58,7.84-18.95,13.59l.02.02-99.8,106.71c1.44,3.85,2.23,8.05,2.23,12.44,0,9.32-3.53,17.76-9.25,23.86-5.71,6.11-13.6,9.89-22.32,9.89s-16.57-3.79-22.3-9.9l-.02.02c-5.71-6.11-9.25-14.55-9.25-23.86s3.53-17.7,9.25-23.82l.02-.02.02-.02c5.73-6.11,13.61-9.89,22.28-9.89,4.11,0,8.04.84,11.64,2.37l99.79-106.7.02.03c7.7-8.22,16.86-14.85,27.02-19.39,9.97-4.45,20.87-6.91,32.27-6.91s22.31,2.46,32.27,6.91c9.1,4.06,17.41,9.81,24.57,16.88l.05-.05c5.41,5.85,11.91,10.58,19.17,13.82,6.95,3.1,14.61,4.82,22.69,4.82s15.74-1.71,22.69-4.82c7.15-3.2,13.58-7.84,18.95-13.59l-.02-.02,99.8-106.7c-1.43-3.85-2.22-8.05-2.22-12.45,0-9.27,3.53-17.7,9.25-23.82l.02-.02.02-.02c5.73-6.11,13.61-9.89,22.28-9.89Z"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="387.83" y1="637.75" x2="785.79" y2="434.31" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fe4eee"/>
      <stop offset=".47" stop-color="#2f81ff"/>
      <stop offset="1" stop-color="#2fdeff"/>
    </linearGradient>
  </defs>
                    <text class="cls-4" transform="translate(301.52 832.59)"><tspan class="cls-9" x="0" y="0">F</tspan><tspan x="57.07" y="0">ACILI</tspan><tspan class="cls-8" x="350.33" y="0">TA</tspan><tspan x="478.89" y="0">TE</tspan></text>
  <g class="cls-7">
    <rect class="cls-1" x="299.53" y="364.75" width="604.93" height="327.05"/>
  </g></svg>
                <h1>You are not permitted to access the requested page.</h1>
                <a href="/">Please click here to go to the home page.</a>
            </div>
        </div>
    </body>
</html>
